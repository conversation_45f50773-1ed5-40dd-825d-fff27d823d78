<resources xmlns:tools="http://schemas.android.com/tools">
    <!-- Base application theme. -->
    <style name="Base.Theme.PhotoFilter" parent="Theme.Material3.DayNight.NoActionBar">
        <!-- Customize your dark theme here. -->
        <!-- <item name="colorPrimary">@color/my_dark_primary</item> -->

        <!-- Status bar colors for dark mode -->
        <item name="android:statusBarColor">@color/ic_launcher_background</item>
        <item name="android:windowLightStatusBar">false</item>

        <!-- Background color for dark mode -->
        <item name="android:windowBackground">@color/ic_launcher_background</item>

        <!-- Primary colors for dark mode -->
        <item name="colorPrimary">@color/ic_launcher_background</item>
        <item name="colorPrimaryVariant">@color/splash_brand_color</item>
        <item name="colorOnPrimary">@color/white</item>
    </style>

    <style name="Theme.PhotoFilter.Splash" parent="Theme.PhotoFilter">
        <item name="android:windowBackground">@drawable/splash_background</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowFullscreen">true</item>
        <item name="android:windowContentOverlay">@null</item>
    </style>

</resources>