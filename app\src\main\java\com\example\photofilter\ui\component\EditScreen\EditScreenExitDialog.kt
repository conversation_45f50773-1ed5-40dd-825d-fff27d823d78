package com.example.photofilter.ui.component.EditScreen

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.size
import androidx.compose.material3.AlertDialog
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp

/**
 * Exit confirmation dialog component for EditScreen
 * Handles save and exit options
 */
@Composable
fun EditScreenExitDialog(
    isVisible: Boolean,
    isSaving: Boolean,
    onDismiss: () -> Unit,
    onDiscardAndExit: () -> Unit,
    onSaveAndExit: () -> Unit,
    modifier: Modifier = Modifier
) {
    if (isVisible) {
        AlertDialog(
            modifier = modifier,
            onDismissRequest = onDismiss,
            title = { Text("Thoát chỉnh sửa") },
            text = { Text("Bạn có thay đổi chưa được lưu. Bạn muốn làm gì?") },
            confirmButton = {
                Row(
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    TextButton(
                        onClick = onDiscardAndExit,
                        enabled = !isSaving
                    ) {
                        Text("Bỏ qua")
                    }
                    TextButton(
                        onClick = onSaveAndExit,
                        enabled = !isSaving
                    ) {
                        if (isSaving) {
                            Row(
                                verticalAlignment = Alignment.CenterVertically,
                                horizontalArrangement = Arrangement.spacedBy(4.dp)
                            ) {
                                CircularProgressIndicator(modifier = Modifier.size(16.dp))
                                Text("Đang lưu...")
                            }
                        } else {
                            Text("Lưu và thoát")
                        }
                    }
                }
            },
            dismissButton = {
                TextButton(
                    onClick = onDismiss,
                    enabled = !isSaving
                ) {
                    Text("Hủy")
                }
            }
        )
    }
}
