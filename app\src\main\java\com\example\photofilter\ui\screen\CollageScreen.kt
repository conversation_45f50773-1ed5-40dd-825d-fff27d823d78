package com.example.photofilter.ui.screen

import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Scaffold
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.onGloballyPositioned
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.unit.IntSize
import androidx.compose.ui.unit.dp
import com.example.photofilter.ui.component.CollageScreen.CollageScreenExitDialog
import com.example.photofilter.ui.component.CollageScreen.CollageScreenImageDisplay
import com.example.photofilter.ui.component.CollageScreen.CollageScreenToolsArea
import com.example.photofilter.ui.component.CollageScreen.CollageScreenTopBar
import com.example.photofilter.ui.effect.CollageScreenBackHandler
import com.example.photofilter.ui.effect.CollageScreenErrorEffect
import com.example.photofilter.ui.effect.CollageScreenExitEffect
import com.example.photofilter.ui.effect.CollageScreenSaveSuccessEffect
import com.example.photofilter.ui.viewmodel.CollageViewModel

/**
 * Main CollageScreen composable following MVVM architecture
 * This screen is now a pure container that delegates all logic to ViewModels and Components
 */
@Composable
fun CollageScreen(
    onBack: () -> Unit,
    viewModel: CollageViewModel,
    modifier: Modifier = Modifier
) {
    var boxSize by remember { mutableStateOf(IntSize.Zero) }
    val context = LocalContext.current
    val uiState by viewModel.uiState.collectAsState()

    // Image picker launcher
    val imagePickerLauncher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.GetMultipleContents()
    ) { uris ->
        if (uris.isNotEmpty()) {
            viewModel.loadImages(uris, context.contentResolver)
        }
    }

    // Exit after save callback
    val exitAfterSaveCallback = remember {
        {
            viewModel.clearCollageData()
            onBack()
        }
    }

    // Handle system back button
    CollageScreenBackHandler {
        viewModel.requestExit()
    }

    // Handle exit requests (when no image or after clearing image data)
    CollageScreenExitEffect(
        shouldExit = uiState.shouldExit,
        onExit = {
            viewModel.resetExitFlag()
            onBack()
        }
    )

    // Show error messages
    CollageScreenErrorEffect(
        errorMessage = uiState.errorMessage,
        onErrorShown = { viewModel.clearError() }
    )

    // Show success messages and handle exit after save
    CollageScreenSaveSuccessEffect(
        statusMessage = uiState.statusMessage,
        shouldExitAfterSave = uiState.shouldExitAfterSave,
        onSuccess = { /* handled in effect */ },
        onExit = exitAfterSaveCallback,
        onStatusCleared = { viewModel.clearStatusMessage() }
    )

    // Main UI
    Scaffold(
        containerColor = Color.Transparent,
        topBar = {
            CollageScreenTopBar(
                hasImages = uiState.images.isNotEmpty(),
                hasCollage = uiState.collageData != null,
                isSaving = uiState.isSaving,
                onBack = { viewModel.requestExit() },
                onClearImages = { viewModel.clearImages() },
                onSaveCollage = { viewModel.saveCollage(context) }
            )
        }
    ) { paddingValues ->
        Box(
            modifier = modifier
                .fillMaxSize()
                .onGloballyPositioned { coordinates ->
                    boxSize = coordinates.size
                }
        ) {
            Box(
                modifier = Modifier
                    .matchParentSize()
                    .background(
                        Brush.radialGradient(
                            colors = listOf(Color(0x80FF2E97), Color.Transparent),
                            center = Offset(boxSize.width*0.2f, boxSize.height*0.5f),
                            radius = 800f
                        )
                    )
            )

            Box(
                modifier = Modifier
                    .matchParentSize()
                    .background(
                        Brush.radialGradient(
                            colors = listOf(Color(0x80A855FF), Color.Transparent),
                            center = Offset(boxSize.width*0.5f, boxSize.height*0.3f),
                            radius = 600f
                        )
                    )
            )

            Box(
                modifier = Modifier
                    .matchParentSize()
                    .background(
                        Brush.radialGradient(
                            colors = listOf(Color(0x8044C2FF), Color.Transparent),
                            center = Offset(boxSize.width*0.8f, boxSize.height*0.7f),
                            radius = 800f
                        )
                    )
            )

            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(paddingValues)
            ) {
                // Image Display Area - giới hạn chiều cao
                CollageScreenImageDisplay(
                    collageBitmap = uiState.collageData?.finalBitmap,
                    statusMessage = uiState.statusMessage,
                    isLoading = uiState.isLoading,
                    imagePickerLauncher = imagePickerLauncher,
                    modifier = Modifier.weight(1f)
                )

                // Show tools area only when images are loaded
                if (uiState.images.isNotEmpty()) {
                    Box(
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(250.dp) // Tăng height để chứa ảnh to hơn
                    ) {
                        CollageScreenToolsArea(
                            images = uiState.images,
                            selectedLayout = uiState.selectedLayout,
                            isLoading = uiState.isLoading,
                            imagePickerLauncher = imagePickerLauncher,
                            onLayoutSelected = { layout -> viewModel.selectLayout(layout) }
                        )
                    }
                }
            }
        }
    }

    // Exit confirmation dialog
    CollageScreenExitDialog(
        isVisible = uiState.showExitDialog,
        hasCollage = uiState.collageData != null,
        isSaving = uiState.isSaving,
        onDismiss = { viewModel.hideExitDialog() },
        onDiscardAndExit = {
            viewModel.hideExitDialog()
            viewModel.clearCollageData()
            onBack()
        },
        onSaveAndExit = {
            viewModel.saveAndExit()
            viewModel.saveCollage(context)
        }
    )
}
