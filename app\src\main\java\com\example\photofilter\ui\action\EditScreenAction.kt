package com.example.photofilter.ui.action

import android.content.Context
import android.net.Uri
import com.example.photofilter.domain.model.AdjustmentData
import com.example.photofilter.domain.model.AspectRatio
import com.example.photofilter.domain.model.CropData
import com.example.photofilter.domain.model.EditTab
import com.example.photofilter.domain.model.FilterType

/**
 * EditScreenAction cho MVVM pattern
 * 
 * Định nghĩa tất cả actions mà user có thể thực hiện trong EditScreen
 */
sealed class EditScreenAction {
    
    // ==================== UI ACTIONS ====================
    data class SelectTab(val tab: EditTab) : EditScreenAction()
    object ShowExitDialog : EditScreenAction()
    object HideExitDialog : EditScreenAction()
    object RequestExit : EditScreenAction()
    object SaveAndExit : EditScreenAction()
    object ResetExitFlag : EditScreenAction()

    // ==================== DOMAIN ACTIONS ====================
    data class LoadImage(val uri: Uri, val context: Context) : EditScreenAction()
    data class SaveImage(val context: Context) : EditScreenAction()
    object ResetToOriginal : EditScreenAction()
    object ClearImageData : EditScreenAction()
    data class RotateImage(val angle: Float = 90f) : EditScreenAction()
    data class UpdateSliderRotation(val value: Float) : EditScreenAction()
    object FlipHorizontal : EditScreenAction()
    data class ApplyFilter(val filterType: FilterType) : EditScreenAction()
    data class UpdateAdjustments(val adjustments: AdjustmentData) : EditScreenAction()
    data class UpdateCropData(val cropData: CropData) : EditScreenAction()
    data class ApplyCropAspectRatio(val aspectRatio: AspectRatio) : EditScreenAction()
    
    // ==================== UTILITY ACTIONS ====================
    object ClearError : EditScreenAction()
    object ClearStatusMessage : EditScreenAction()
}
