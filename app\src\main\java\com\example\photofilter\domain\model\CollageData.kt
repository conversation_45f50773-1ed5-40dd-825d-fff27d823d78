package com.example.photofilter.domain.model

import android.graphics.Bitmap
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.GridOn
import androidx.compose.material.icons.filled.GridView
import androidx.compose.ui.graphics.vector.ImageVector

data class CollageData(
    val images: List<Bitmap> = emptyList(),
    val layout: CollageLayout = CollageLayout.GRID_2X2,
    val finalBitmap: Bitmap? = null
)

enum class CollageLayout(
    val displayName: String,
    val maxImages: Int,
    val icon: ImageVector
) {
    GRID_2X2("2x2 Grid", 4, Icons.Default.GridView),
    GRID_3X3("3x3 Grid", 9, Icons.Default.GridOn),
    HORIZONTAL_2("2 Ngang", 2, Icons.Default.GridView),
    VERTICAL_2("2 Dọc", 2, Icons.Default.GridView),
    HORIZONTAL_3("3 Ngang", 3, Icons.Default.GridView),
    VERTICAL_3("3 Dọc", 3, Icons.Default.GridView)
}
