package com.example.photofilter.domain.model

import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Crop
import androidx.compose.material.icons.filled.Filter
import androidx.compose.material.icons.filled.Rotate90DegreesCcw
import androidx.compose.material.icons.filled.WbSunny
import androidx.compose.ui.graphics.vector.ImageVector

enum class EditTab(
    val displayName: String,
    val icon: ImageVector
) {
    TRANSFORM("Chuyển đổi", Icons.Default.Rotate90DegreesCcw),
    FILTER("Bộ lọc", Icons.Default.Filter),
    ADJUST( "Điều chỉnh", Icons.Default.WbSunny),
    CROP("Cắ<PERSON> ảnh", Icons.Default.Crop)
}