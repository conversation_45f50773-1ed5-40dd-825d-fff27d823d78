package com.example.photofilter.ui.component.EditTools

import androidx.compose.foundation.horizontalScroll
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material.Slider
import androidx.compose.material.SliderDefaults
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import com.example.photofilter.domain.model.AdjustmentData
import com.example.photofilter.domain.model.AdjustmentType

@Composable
fun AdjustmentTools(
    adjustmentData: AdjustmentData,
    onAdjustmentChange: (AdjustmentData) -> Unit,
    enabled: Boolean = true,
    modifier: Modifier = Modifier
) {
    var currentAdjustment by remember { mutableStateOf(AdjustmentType.BRIGHTNESS)}

    Column(
        modifier = modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        Text(
            text = currentAdjustment.displayName,
            style = MaterialTheme.typography.labelMedium,
            modifier = Modifier.fillMaxWidth(),
            textAlign = TextAlign.Center
        )

        Spacer(modifier = Modifier.height(2.dp))

        Box(
            modifier = Modifier.fillMaxWidth(),
            contentAlignment = Alignment.Center
        ){
            Row(
                modifier = Modifier
                    .horizontalScroll(rememberScrollState()),
                horizontalArrangement = Arrangement.spacedBy(8.dp)
    //            horizontalArrangement = Arrangement.Center
            ){
                AdjustmentType.entries.forEach { adjustType ->
                    Card(
                        onClick = {
                            if(adjustType!=currentAdjustment) currentAdjustment = adjustType
                            else {
                                onAdjustmentChange(adjustmentData.resetAdjustment(adjustType))
                            }
                        },
                        modifier = modifier
                            .width(50.dp)
                            .height(50.dp),
                        colors = CardDefaults.cardColors(
                            containerColor = (
                                if(adjustType==currentAdjustment) MaterialTheme.colorScheme.onSurface.copy(alpha = 0.5f)
                                else if(!adjustmentData.isAtDefault(adjustType)) MaterialTheme.colorScheme.onSurface.copy(alpha = 0.2f)
                                else MaterialTheme.colorScheme.surface.copy(alpha = 0.5f)
                            ),
                            contentColor = MaterialTheme.colorScheme.onSurface
                        ),
                        shape = CircleShape,
                        enabled = enabled
                    ){
                        Column(
                            verticalArrangement = Arrangement.Center,
                            horizontalAlignment = Alignment.CenterHorizontally,
                            modifier = Modifier.fillMaxWidth().fillMaxHeight()
                        ){
                            if(currentAdjustment == adjustType) {
                                Text(
                                    text = String.format("%.1f", adjustmentData.getAdjustmentValue(currentAdjustment)),
                                    style = MaterialTheme.typography.labelMedium,
                                    textAlign = TextAlign.Center
                                )
                            }
                            else {
                                Icon(
                                    imageVector = adjustType.icon,
                                    contentDescription = null,
                                    modifier = Modifier.size(24.dp),
                                    tint = MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.6f)
                                )
                            }
                        }
                    }
                }
            }
        }

        Spacer(modifier = Modifier.height(2.dp))

        Slider(
            value = adjustmentData.getAdjustmentValue(currentAdjustment),
            valueRange = currentAdjustment.range,
            steps = currentAdjustment.steps,
            onValueChange = { value ->
                onAdjustmentChange(adjustmentData.updateAdjustment(currentAdjustment, value))
            },
            enabled = enabled,
            modifier = Modifier.fillMaxWidth().padding(horizontal = 24.dp),
            colors = SliderDefaults.colors(
                thumbColor = MaterialTheme.colorScheme.primary.copy(0.8f),
                activeTrackColor = MaterialTheme.colorScheme.primary.copy(0.8f),
                activeTickColor = Color.Transparent,
                inactiveTickColor = Color.Transparent
            )
        )
    }
}

