package com.example.photofilter.ui.effect

import android.widget.Toast
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.platform.LocalContext

@Composable
fun CollageScreenSaveSuccessEffect(
    statusMessage: String,
    shouldExitAfterSave: Boolean,
    onSuccess: () -> Unit,
    onExit: () -> Unit,
    onStatusCleared: () -> Unit
) {
    val context = LocalContext.current
    
    LaunchedEffect(statusMessage) {
        if (statusMessage.contains("thành công")) {
            Toast.makeText(context, statusMessage, Toast.LENGTH_SHORT).show()
            onSuccess()
            
            if (shouldExitAfterSave) {
                onExit()
            }
            
            onStatusCleared()
        }
    }
}
