package com.example.photofilter.di

import android.app.Application

/**
 * PhotoFilterApplication - Application class chính của app PhotoFilter
 * 
 * === MỤC ĐÍCH ===
 * - Quản lý lifecycle của toàn bộ application
 * - Xử lý memory management cho image processing
 * - Cleanup resources khi system thiếu memory
 * - Initialize global dependencies nếu cần
 * 
 * === MEMORY MANAGEMENT STRATEGY ===
 * - Tự động clear cache khi memory thấp
 * - Force garbage collection trong trường hợp critical
 * - Monitor memory usage để tránh OOM crashes
 * 
 * === CÁCH HOẠT ĐỘNG ===
 * 1. System gọi onLowMemory() khi RAM sắp hết
 * 2. App clear tất cả image cache
 * 3. Force GC để giải phóng memory ngay lập tức
 * 4. App tiếp tục hoạt động mượt mà
 * 
 * === ĐĂNG KÝ TRONG MANIFEST ===
 * ```xml
 * <application
 *     android:name=".di.PhotoFilterApplication"
 *     ... >
 * ```
 */
class PhotoFilterApplication : Application() {

    /**
     * Được gọi khi system cực kỳ thiếu memory
     * 
     * === KHI NÀO XẢY RA ===
     * - RAM của device gần như đầy
     * - System cần memory gấp để tránh crash
     * - Thường xảy ra khi process nhiều ảnh lớn
     * 
     * === HÀNH ĐỘNG ===
     * - Clear toàn bộ image cache ngay lập tức
     * - Force garbage collection
     * - Giải phóng memory để app không bị kill
     */
    override fun onLowMemory() {
        super.onLowMemory()

        // Clear toàn bộ image cache để giải phóng memory
        DependencyContainer.clearImageCache()

        // Force garbage collection ngay lập tức
        System.gc()
    }

    /**
     * Được gọi khi system cần trim memory với các level khác nhau
     * 
     * === CÁC MEMORY LEVEL ===
     * - TRIM_MEMORY_UI_HIDDEN: App không còn visible
     * - TRIM_MEMORY_BACKGROUND: App ở background
     * - TRIM_MEMORY_MODERATE: System cần memory nhẹ
     * - TRIM_MEMORY_COMPLETE: System cực kỳ cần memory
     * 
     * === STRATEGY ===
     * - Level thấp: Clear một phần cache
     * - Level cao: Clear toàn bộ + force GC
     * 
     * @param level Memory trim level từ system
     */
    override fun onTrimMemory(level: Int) {
        super.onTrimMemory(level)

        when (level) {
            // App không còn visible cho user
            TRIM_MEMORY_UI_HIDDEN,
            // App đang ở background mode
            TRIM_MEMORY_BACKGROUND,
            // System cần memory ở mức vừa phải
            TRIM_MEMORY_MODERATE -> {
                // Clear cache để giải phóng memory cho foreground apps
                DependencyContainer.clearImageCache()
            }
            
            // System CỰC KỲ cần memory, risk bị kill cao
            TRIM_MEMORY_COMPLETE -> {
                // Clear tất cả cache có thể
                DependencyContainer.clearImageCache()
                
                // Force garbage collection ngay lập tức
                System.gc()
            }
        }
    }
}
