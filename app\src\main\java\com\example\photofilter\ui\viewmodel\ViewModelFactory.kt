package com.example.photofilter.ui.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.ViewModelProvider
import com.example.photofilter.di.DependencyContainer

class ViewModelFactory : ViewModelProvider.Factory {
    
    @Suppress("UNCHECKED_CAST")
    override fun <T : ViewModel> create(modelClass: Class<T>): T {
        return when (modelClass) {
            NavigationViewModel::class.java -> {
                NavigationViewModel() as T
            }
            EditViewModel::class.java -> {
                // Simple MVVM EditViewModel
                EditViewModel(DependencyContainer.imageRepository) as T
            }
            CollageViewModel::class.java -> {
                CollageViewModel(DependencyContainer.collageRepository) as T
            }
            else -> throw IllegalArgumentException("Unknown ViewModel class: ${modelClass.name}")
        }
    }
}
