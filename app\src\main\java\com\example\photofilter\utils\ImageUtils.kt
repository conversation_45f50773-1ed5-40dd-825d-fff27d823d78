package com.example.photofilter.utils

import android.content.ContentResolver
import android.content.ContentValues
import android.content.Context
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.net.Uri
import android.os.Build
import android.os.Environment
import android.provider.MediaStore
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.IOException

/**
 * Bộ tiện ích xử lý ảnh thống nhất - Tải, lưu và các thao tác cơ bản
 * 
 * === MỤC ĐÍCH CHÍNH ===
 * - <PERSON>ad ảnh từ URI với tối ưu bộ nhớ (sample size, config)
 * - Lưu ảnh vào Gallery với metadata đầy đủ
 * - Tạo thumbnail và preview với kích thước tối ưu
 * - Cung cấp thông tin ảnh (size, memory usage, etc.)
 * - <PERSON><PERSON> lý lỗi an toàn (OutOfMemory, IO exceptions)
 * 
 * === CHIẾN LƯỢC TỐI ƯU ===
 * - Two-pass loading: Lần 1 đọc metadata, lần 2 decode với sample size
 * - Smart sample size: Tự động tính toán để giảm memory
 * - Config optimization: RGB_565 cho preview, ARGB_8888 cho chính
 * - Coroutines: Background processing, không block UI
 * - Result wrapper: Safe error handling
 */
object ImageUtils {

    // ==================== CONSTANTS - HẰNG SỐ CẤU HÌNH ====================
    
    // Kích thước tối đa cho ảnh chính (2K resolution)
    // Lý do chọn 2048: Cân bằng quality vs memory, phù hợp với mobile
    private const val MAX_IMAGE_SIZE = 2048
    
    // ==================== IMAGE LOADING - TẢI ẢNH TỐI ƯU ====================
    
    /**
     * Load ảnh tối ưu từ URI với giới hạn kích thước
     * 
     * @param uri: URI của ảnh (content://, file://)
     * @param contentResolver: ContentResolver để access ảnh
     * @param maxSize: Kích thước tối đa (width hoặc height)
     * @return: Result<Bitmap> - Success với bitmap hoặc Failure với exception
     * 
     * === TWO-PASS LOADING STRATEGY ===
     * Pass 1: inJustDecodeBounds = true
     * - Chỉ đọc metadata (width, height, format)
     * - Không load pixel data → Rất nhanh, ít memory
     * - Tính toán sample size dựa trên dimensions
     * 
     * Pass 2: inJustDecodeBounds = false
     * - Load thực sự với sample size đã tính
     * - Bitmap size đã được giảm đáng kể
     * - Memory usage được kiểm soát
     * 
     * === SAMPLE SIZE STRATEGY ===
     * - Sample size = 2: Ảnh giảm 1/4 memory (1/2 width, 1/2 height)
     * - Sample size = 4: Ảnh giảm 1/16 memory
     * - Tự động tính để fit trong maxSize
     */
    suspend fun loadOptimizedImage(
        uri: Uri, 
        contentResolver: ContentResolver,
        maxSize: Int = MAX_IMAGE_SIZE
    ): Result<Bitmap> = withContext(Dispatchers.IO) {
        try {
            // Pass 1: Đọc metadata để tính sample size
            val options = BitmapFactory.Options().apply {
                inJustDecodeBounds = true // Chỉ đọc thông tin, không load pixels
                inPreferredConfig = Bitmap.Config.RGB_565 // Ít memory hơn ARGB_8888
            }

            contentResolver.openInputStream(uri)?.use { inputStream ->
                BitmapFactory.decodeStream(inputStream, null, options)
            }

            // Tính sample size dựa trên dimensions thực tế
            options.inSampleSize = calculateInSampleSize(options, maxSize, maxSize)
            options.inJustDecodeBounds = false // Giờ load thực sự

            // Pass 2: Decode với sample size đã tối ưu
            val bitmap = contentResolver.openInputStream(uri)?.use { inputStream ->
                BitmapFactory.decodeStream(inputStream, null, options)
            }

            if (bitmap != null) {
                Result.success(bitmap)
            } else {
                Result.failure(IOException("Failed to decode bitmap"))
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * Load bitmap from URI (simple version)
     */
    suspend fun loadBitmapFromUri(uri: Uri, contentResolver: ContentResolver): Bitmap? {
        return loadOptimizedImage(uri, contentResolver).getOrNull()
    }

    /**
     * Load bitmap with specific size options
     */
    suspend fun loadBitmapFromUriWithOptions(
        uri: Uri, 
        contentResolver: ContentResolver,
        maxWidth: Int = 1024,
        maxHeight: Int = 1024
    ): Result<Bitmap> {
        val maxSize = minOf(maxWidth, maxHeight)
        return loadOptimizedImage(uri, contentResolver, maxSize)
    }

    /**
     * Save bitmap to gallery with comprehensive error handling
     */
    suspend fun saveBitmapToGallery(
        context: Context, 
        bitmap: Bitmap, 
        fileName: String,
        filePrefix: String = "PhotoFilter"
    ): Result<Boolean> {
        return withContext(Dispatchers.IO) {
            try {
                // Check storage permission
                if (!PermissionUtils.hasStoragePermission(context)) {
                    return@withContext Result.failure(Exception("Cần quyền truy cập bộ nhớ để lưu ảnh"))
                }

                // Check available storage space
                if (!hasEnoughStorage(context, bitmap)) {
                    return@withContext Result.failure(Exception("Không đủ dung lượng để lưu ảnh"))
                }

                // Optimize bitmap for saving
                val optimizedBitmap = optimizeBitmapForSave(bitmap)

                val filename = if (fileName.isNotEmpty()) "$fileName.jpg" 
                              else "${filePrefix}_${System.currentTimeMillis()}.jpg"
                
                val contentValues = ContentValues().apply {
                    put(MediaStore.MediaColumns.DISPLAY_NAME, filename)
                    put(MediaStore.MediaColumns.MIME_TYPE, "image/jpeg")
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                        put(MediaStore.MediaColumns.RELATIVE_PATH, Environment.DIRECTORY_PICTURES)
                        put(MediaStore.MediaColumns.IS_PENDING, 1)
                    }
                }

                val uri = context.contentResolver.insert(
                    MediaStore.Images.Media.EXTERNAL_CONTENT_URI,
                    contentValues
                )

                uri?.let {
                    context.contentResolver.openOutputStream(it)?.use { outputStream ->
                        val success = optimizedBitmap.compress(Bitmap.CompressFormat.JPEG, 90, outputStream)
                        if (!success) {
                            return@withContext Result.failure(Exception("Không thể nén ảnh để lưu"))
                        }
                    }

                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                        contentValues.clear()
                        contentValues.put(MediaStore.MediaColumns.IS_PENDING, 0)
                        context.contentResolver.update(it, contentValues, null, null)
                    }

                    Result.success(true)
                } ?: Result.failure(IOException("Cannot create file to save"))

            } catch (e: SecurityException) {
                Result.failure(Exception("Không có quyền lưu ảnh: ${e.message}"))
            } catch (e: IOException) {
                Result.failure(Exception("Lỗi ghi file: ${e.message}"))
            } catch (e: OutOfMemoryError) {
                Result.failure(Exception("Không đủ bộ nhớ để xử lý ảnh"))
            } catch (e: Exception) {
                Result.failure(Exception("Lỗi không xác định: ${e.message}"))
            }
        }
    }

    // ==================== BITMAP OPERATIONS ====================
    
    /**
     * Create thumbnail with specified size
     */
    fun createThumbnail(bitmap: Bitmap, size: Int = 200): Bitmap {
        val ratio = minOf(
            size.toFloat() / bitmap.width,
            size.toFloat() / bitmap.height
        )

        if (ratio >= 1f) return bitmap

        val newWidth = (bitmap.width * ratio).toInt()
        val newHeight = (bitmap.height * ratio).toInt()

        return Bitmap.createScaledBitmap(bitmap, newWidth, newHeight, true)
    }
    
    /**
     * Create bitmap with safe config fallback
     */
    fun createSafeBitmap(width: Int, height: Int, config: Bitmap.Config?): Bitmap {
        val safeConfig = config ?: Bitmap.Config.ARGB_8888
        return androidx.core.graphics.createBitmap(width, height, safeConfig)
    }
    
    /**
     * Check if image is large (exceeds MAX_IMAGE_SIZE)
     */
    fun isLargeImage(bitmap: Bitmap): Boolean {
        return bitmap.width > MAX_IMAGE_SIZE || bitmap.height > MAX_IMAGE_SIZE
    }
    
    /**
     * Get image memory size in bytes
     */
    fun getImageMemorySize(bitmap: Bitmap): Long {
        return bitmap.byteCount.toLong()
    }
    
    /**
     * Get formatted image info string
     */
    fun getImageInfo(bitmap: Bitmap): String {
        return "${bitmap.width}x${bitmap.height} (${getImageMemorySize(bitmap) / 1024}KB)"
    }

    // ==================== HELPER FUNCTIONS ====================
    
    /**
     * Calculate optimal sample size for image loading
     */
    fun calculateInSampleSize(options: BitmapFactory.Options, reqWidth: Int, reqHeight: Int): Int {
        val (height: Int, width: Int) = options.run { outHeight to outWidth }
        var inSampleSize = 1

        if (height > reqHeight || width > reqWidth) {
            val halfHeight: Int = height / 2
            val halfWidth: Int = width / 2

            while (halfHeight / inSampleSize >= reqHeight && halfWidth / inSampleSize >= reqWidth) {
                inSampleSize *= 2
            }
        }
        return inSampleSize
    }

    /**
     * Check if there's enough storage space
     */
    fun hasEnoughStorage(context: Context, bitmap: Bitmap): Boolean {
        return try {
            val bytesNeeded = bitmap.byteCount.toLong()
            val availableBytes = context.getExternalFilesDir(null)?.freeSpace ?: 0L
            availableBytes > bytesNeeded * 2 // 2x buffer for safety
        } catch (e: Exception) {
            true // If we can't check, assume it's okay
        }
    }

    /**
     * Optimize bitmap for saving (compress if too large)
     */
    fun optimizeBitmapForSave(bitmap: Bitmap): Bitmap {
        val maxSize = 2048
        return if (isLargeImage(bitmap)) {
            createThumbnail(bitmap, maxSize)
        } else {
            bitmap
        }
    }
}
