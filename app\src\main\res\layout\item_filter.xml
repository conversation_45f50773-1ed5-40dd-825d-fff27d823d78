<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <TextView
        android:id="@+id/textFilterName"
        android:layout_width="63dp"
        android:layout_height="53dp"
        android:layout_margin="6dp"
        android:background="#DDD"
        android:padding="10dp"
        android:textColor="#000"
        tools:ignore="MissingConstraints" />

</androidx.constraintlayout.widget.ConstraintLayout>