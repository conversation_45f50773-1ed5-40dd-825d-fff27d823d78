package com.example.photofilter.domain.model

import android.graphics.Bitmap

data class ImageData(
    val originalBitmap: Bitmap,
    val currentBitmap: Bitmap,

    val transformData: TransformData = TransformData(),
    val filterData: FilterData = FilterData(),
    val adjustmentData: AdjustmentData = AdjustmentData(),
    val cropData: CropData = CropData(),
) {
    fun hasUnsavedChanges(): Boolean {
        return !transformData.isDefault() ||
                !filterData.isDefault() ||
                !adjustmentData.isDefault() ||
                !cropData.isDefault()
    }
}
