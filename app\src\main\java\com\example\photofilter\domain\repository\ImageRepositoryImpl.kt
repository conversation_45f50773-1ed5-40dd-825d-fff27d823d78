package com.example.photofilter.domain.repository

import android.content.ContentResolver
import android.content.Context
import android.graphics.Bitmap
import android.net.Uri
import com.example.photofilter.utils.ImageCacheManager
import com.example.photofilter.domain.model.AdjustmentData
import com.example.photofilter.domain.model.FilterType
import com.example.photofilter.domain.model.ImageData
import com.example.photofilter.domain.repository.ImageRepository
import com.example.photofilter.utils.ImageProcessors
import com.example.photofilter.utils.ImageUtils
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

class ImageRepositoryImpl(
    override val imageProcessors: ImageProcessors
) : ImageRepository {

    private val cacheManager = ImageCacheManager()

    override suspend fun loadImageFromUri(uri: Uri, contentResolver: ContentResolver): Result<Bitmap> =
        ImageUtils.loadBitmapFromUriWithOptions(uri, contentResolver, 1024, 1024)

    override suspend fun saveImageToGallery(context: Context, bitmap: Bitmap, fileName: String): Result<Boolean> =
        ImageUtils.saveBitmapToGallery(context, bitmap, fileName, "PhotoFilter")

    override suspend fun applyFilter(imageData: ImageData, filterType: FilterType): Result<ImageData> =
        withContext(Dispatchers.Default) {
            try {
                // Cập nhật filter data trước
                val updatedImageData = imageData.copy(
                    filterData = imageData.filterData.copy(appliedFilter = filterType)
                )
                
                // Áp dụng tất cả transforms, crop, adjustments với filter mới
                getBaseTransformedBitmap(updatedImageData).fold(
                    onSuccess = { transformedBitmap ->
                        Result.success(updatedImageData.copy(currentBitmap = transformedBitmap))
                    },
                    onFailure = { error ->
                        Result.failure(error)
                    }
                )
            } catch (e: Exception) {
                Result.failure(e)
            }
        }

    override suspend fun rotateImage(imageData: ImageData, angle: Float): Result<ImageData> =
        withContext(Dispatchers.Default) {
            try {
                // Cập nhật transform data trước
                val newAngle = (imageData.transformData.rotateAngle + angle) % 360f
                val updatedImageData = imageData.copy(
                    transformData = imageData.transformData.copy(rotateAngle = newAngle)
                )

                // Áp dụng tất cả transforms, filters và adjustments với rotation mới
                getBaseTransformedBitmap(updatedImageData).fold(
                    onSuccess = { transformedBitmap ->
                        Result.success(updatedImageData.copy(currentBitmap = transformedBitmap))
                    },
                    onFailure = { error ->
                        Result.failure(error)
                    }
                )
            } catch (e: Exception) {
                Result.failure(e)
            }
        }

    override suspend fun flipHorizontal(imageData: ImageData): Result<ImageData> =
        withContext(Dispatchers.Default) {
            try {
                // Cập nhật transform data trước
                val newFlipHorizontal = if (imageData.transformData.flipHorizontal == 0) 1 else 0
                val updatedImageData = imageData.copy(
                    transformData = imageData.transformData.copy(flipHorizontal = newFlipHorizontal)
                )

                // Áp dụng tất cả transforms, filters và adjustments với flip mới
                getBaseTransformedBitmap(updatedImageData).fold(
                    onSuccess = { transformedBitmap ->
                        Result.success(updatedImageData.copy(currentBitmap = transformedBitmap))
                    },
                    onFailure = { error ->
                        Result.failure(error)
                    }
                )
            } catch (e: Exception) {
                Result.failure(e)
            }
        }

    override suspend fun updateSliderRotation(imageData: ImageData, rotation: Float): Result<ImageData> =
        withContext(Dispatchers.Default) {
            try {
                // Cập nhật slider rotation trước
                val updatedImageData = imageData.copy(
                    transformData = imageData.transformData.copy(sliderRotation = rotation)
                )

                // Áp dụng tất cả transforms, filters và adjustments với slider rotation mới
                getBaseTransformedBitmap(updatedImageData).fold(
                    onSuccess = { transformedBitmap ->
                        Result.success(updatedImageData.copy(currentBitmap = transformedBitmap))
                    },
                    onFailure = { error ->
                        Result.failure(error)
                    }
                )
            } catch (e: Exception) {
                Result.failure(e)
            }
        }

    override suspend fun resetToOriginal(imageData: ImageData): Result<ImageData> =
        withContext(Dispatchers.Default) {
            try {
                val originalBitmap = imageData.originalBitmap
                val copiedBitmap = originalBitmap.config?.let { config ->
                    originalBitmap.copy(config, false)
                } ?: originalBitmap
                
                // Reset tất cả dữ liệu về mặc định
                Result.success(
                    imageData.copy(
                        currentBitmap = copiedBitmap,
                        transformData = com.example.photofilter.domain.model.TransformData(),
                        filterData = com.example.photofilter.domain.model.FilterData(),
                        adjustmentData = AdjustmentData(),
                        cropData = com.example.photofilter.domain.model.CropData()
                    )
                )
            } catch (e: Exception) {
                Result.failure(e)
            }
        }

    // ==================== NEW REPOSITORY METHODS ====================
    
    /**
     * Tải ảnh từ URI và tạo ImageData với tối ưu memory
     */
    override suspend fun loadOptimizedImageFromUri(uri: Uri, contentResolver: ContentResolver): Result<ImageData> =
        withContext(Dispatchers.Default) {
            try {
                ImageUtils.loadOptimizedImage(uri, contentResolver)
                    .fold(
                        onSuccess = { bitmap ->
                            val imageData = bitmap.config?.let { bitmap.copy(it, false) }?.let {
                                ImageData(
                                    originalBitmap = bitmap,
                                    currentBitmap = it
                                )
                            }
                            if (imageData != null) {
                                Result.success(imageData)
                            } else {
                                Result.failure(Exception("Không thể tạo ImageData từ bitmap"))
                            }
                        },
                        onFailure = { error ->
                            Result.failure(error)
                        }
                    )
            } catch (e: Exception) {
                Result.failure(e)
            }
        }

    /**
     * Lấy bitmap với các transform cơ bản (bao gồm cả filter và adjustments)
     * Updated: Sử dụng pipeline để duy trì tất cả các thay đổi, bao gồm crop
     */
    override suspend fun getBaseTransformedBitmap(imageData: ImageData): Result<Bitmap> =
        withContext(Dispatchers.Default) {
            try {
                var bitmap = imageData.originalBitmap

                // Bước 1: Áp dụng rotation và flip transforms
                if (imageData.transformData.rotateAngle != 0f || imageData.transformData.sliderRotation != 0f) {
                    val totalRotation = imageData.transformData.rotateAngle + imageData.transformData.sliderRotation
                    bitmap = imageProcessors.rotateImage(bitmap, totalRotation)
                }

                if (imageData.transformData.flipHorizontal % 2 == 1) {
                    bitmap = imageProcessors.flipHorizontal(bitmap)
                }

                // Bước 2: Áp dụng crop nếu có
                if (imageData.cropData.aspectRatio != null && imageData.cropData.aspectRatio > 0) {
                    bitmap = imageProcessors.cropToAspectRatio(bitmap, imageData.cropData.aspectRatio)
                }

                // Bước 3: Áp dụng filter nếu có
                if (imageData.filterData.appliedFilter != FilterType.ORIGINAL) {
                    bitmap = imageProcessors.applyFilterSync(bitmap, imageData.filterData.appliedFilter)
                }

                // Bước 4: Áp dụng adjustments nếu có
                if (!imageData.adjustmentData.isDefault()) {
                    bitmap = imageProcessors.applyAdjustments(
                        bitmap = bitmap,
                        brightness = imageData.adjustmentData.brightness,
                        contrast = imageData.adjustmentData.contrast,
                        saturation = imageData.adjustmentData.saturation,
                        blur = imageData.adjustmentData.blur,
                        sharpen = imageData.adjustmentData.sharpen
                    )
                }

                Result.success(bitmap)
            } catch (e: Exception) {
                Result.failure(e)
            }
        }

    /**
     * Áp dụng adjustments (brightness, contrast, saturation, blur, sharpen)
     * Updated: Duy trì tất cả transforms, crop và filters hiện có
     */
    override suspend fun applyAdjustments(
        imageData: ImageData,
        brightness: Float,
        contrast: Float,
        saturation: Float,
        blur: Float,
        sharpen: Float
    ): Result<ImageData> = withContext(Dispatchers.Default) {
        try {
            // Cập nhật adjustment data trước
            val updatedImageData = imageData.copy(
                adjustmentData = AdjustmentData(
                    brightness = brightness,
                    contrast = contrast,
                    saturation = saturation,
                    blur = blur,
                    sharpen = sharpen
                )
            )
            
            // Áp dụng tất cả transforms, crop, filters với adjustments mới
            getBaseTransformedBitmap(updatedImageData).fold(
                onSuccess = { transformedBitmap ->
                    Result.success(updatedImageData.copy(currentBitmap = transformedBitmap))
                },
                onFailure = { error ->
                    Result.failure(error)
                }
            )
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * Áp dụng crop theo aspect ratio
     * Updated: Duy trì tất cả transforms, filters và adjustments hiện có
     */
    override suspend fun applyCropAspectRatio(imageData: ImageData, aspectRatio: Float): Result<ImageData> =
        withContext(Dispatchers.Default) {
            try {
                // Cập nhật crop data trước
                val updatedImageData = imageData.copy(
                    cropData = imageData.cropData.copy(aspectRatio = aspectRatio)
                )

                // Áp dụng tất cả transforms, filters và adjustments với crop mới
                getBaseTransformedBitmap(updatedImageData).fold(
                    onSuccess = { transformedBitmap ->
                        Result.success(updatedImageData.copy(currentBitmap = transformedBitmap))
                    },
                    onFailure = { error ->
                        Result.failure(error)
                    }
                )
            } catch (e: Exception) {
                Result.failure(e)
            }
        }
}
