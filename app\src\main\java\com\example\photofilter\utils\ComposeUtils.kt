package com.example.photofilter.utils

import android.graphics.Bitmap
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.remember
import androidx.compose.ui.graphics.asImageBitmap

/**
 * Compose utilities for performance optimization
 */

/**
 * Remember stable bitmap để tránh recomposition không cần thiết
 * Khi bitmap thay đổi, chỉ recompose khi thực sự cần thiết
 */
@Composable
fun rememberStableBitmap(bitmap: Bitmap?): StableBitmap? {
    return remember(bitmap) {
        bitmap?.let { StableBitmap(it) }
    }
}

/**
 * Optimized LaunchedEffect với better key management
 * Giúp tránh unnecessary effects khi keys không thay đổi
 */
@Composable
fun OptimizedLaunchedEffect(
    vararg keys: Any?,
    block: suspend () -> Unit
) {
    LaunchedEffect(keys = keys) {
        block()
    }
}

/**
 * Wrapper class để làm bitmap stable trong Compose
 */
data class StableBitmap(
    private val bitmap: Bitmap
) {
    val imageBitmap = bitmap.asImageBitmap()
    
    /**
     * Get original bitmap nếu cần
     */
    fun getBitmap(): Bitmap = bitmap
}
