package com.example.photofilter

import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.result.contract.ActivityResultContracts
import androidx.activity.viewModels
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import com.example.photofilter.di.DependencyContainer
import com.example.photofilter.domain.model.Screen
import com.example.photofilter.ui.screen.CollageScreen
import com.example.photofilter.ui.screen.EditScreen
import com.example.photofilter.ui.screen.HomeScreen
import com.example.photofilter.ui.screen.PermissionDeniedDialog
import com.example.photofilter.ui.screen.PermissionRationaleDialog
import com.example.photofilter.ui.theme.PhotoFilterTheme
import com.example.photofilter.ui.viewmodel.CollageViewModel
import com.example.photofilter.ui.viewmodel.EditViewModel
import com.example.photofilter.ui.viewmodel.NavigationViewModel
import com.example.photofilter.ui.viewmodel.ViewModelFactory
import com.example.photofilter.utils.PermissionUtils

class MainActivity : ComponentActivity() {

    private val viewModelFactory = ViewModelFactory()
    private val navigationViewModel: NavigationViewModel by viewModels { viewModelFactory }
    private val editViewModel: EditViewModel by viewModels { viewModelFactory }
    private val collageViewModel: CollageViewModel by viewModels { viewModelFactory }

    private var permissionState = mutableStateOf(PermissionState.CHECKING)

    private val permissionLauncher = registerForActivityResult(
        ActivityResultContracts.RequestMultiplePermissions()
    ) { permissions ->
        val allGranted = permissions.values.all { it }
        if (allGranted) {
            permissionState.value = PermissionState.GRANTED
        } else {
            permissionState.value = PermissionState.DENIED
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        // Check permissions immediately
        checkPermissions()

        setContent {
            PhotoFilterTheme {
                Surface(
                    modifier = Modifier.fillMaxSize(),
                    color = MaterialTheme.colorScheme.background
                ) {
                    val currentPermissionState by permissionState
                    var showRationaleDialog by remember { mutableStateOf(false) }
                    var showDeniedDialog by remember { mutableStateOf(false) }

                    // Handle permission state changes
                    when (currentPermissionState) {
                        PermissionState.RATIONALE -> {
                            showRationaleDialog = true
                        }
                        PermissionState.DENIED -> {
                            // Nếu người dùng đã từ chối trước đó (không còn hiện rationale)
                            // thì hiển thị hướng dẫn đến cài đặt
                            if (PermissionUtils.shouldShowRationale(this@MainActivity)) {
                                showRationaleDialog = true
                            } else {
                                showDeniedDialog = true
                            }
                        }
                        else -> {
                            showRationaleDialog = false
                            showDeniedDialog = false
                        }
                    }

                    // Show permission dialogs
                    when {
                        showRationaleDialog -> {
                            PermissionRationaleDialog(
                                onAllow = {
                                    showRationaleDialog = false
                                    requestPermissions()
                                },
                                onDeny = {
                                    showRationaleDialog = false
                                    permissionState.value = PermissionState.DENIED
                                }
                            )
                        }
                        showDeniedDialog -> {
                            PermissionDeniedDialog(
                                onOpenSettings = {
                                    PermissionUtils.openAppSettings(this@MainActivity)
                                },
                                onExit = {
                                    finish()
                                }
                            )
                        }
                    }

                    // Show main app content only if permission is granted
                    if (currentPermissionState == PermissionState.GRANTED) {
                        val currentScreen by navigationViewModel.currentScreen.collectAsState()

                        when (currentScreen) {
                            Screen.HOME -> {
                                HomeScreen(
                                    onNavigate = { screen -> 
                                        navigationViewModel.navigateTo(screen) 
                                    }
                                )
                            }
                            Screen.EDIT -> {
                                EditScreen(
                                    onBack = {
                                        navigationViewModel.navigateTo(Screen.HOME)
                                    },
                                    editViewModel = editViewModel
                                )
                            }
                            Screen.COLLAGE -> {
                                CollageScreen(
                                    onBack = {
                                        navigationViewModel.navigateTo(Screen.HOME)
                                    },
                                    viewModel = collageViewModel
                                )
                            }
                        }
                    }
                }
            }
        }
    }

    private fun checkPermissions() {
        when {
            PermissionUtils.hasPermissions(this) -> {
                permissionState.value = PermissionState.GRANTED
            }
            PermissionUtils.shouldShowRationale(this) -> {
                permissionState.value = PermissionState.RATIONALE
            }
            else -> {
                requestPermissions()
            }
        }
    }

    private fun requestPermissions() {
        permissionLauncher.launch(PermissionUtils.getRequiredPermissions())
    }

    override fun onResume() {
        super.onResume()
        // Re-check permissions when returning to app (e.g., from settings)
        if (permissionState.value != PermissionState.GRANTED && PermissionUtils.hasPermissions(this)) {
            permissionState.value = PermissionState.GRANTED
        }
    }

    override fun onPause() {
        super.onPause()
        // Clear cache when app goes to background
        DependencyContainer.clearImageCache()
    }

    override fun onDestroy() {
        super.onDestroy()
        // Final cleanup
        DependencyContainer.clearImageCache()
        System.gc()
    }
}

enum class PermissionState {
    CHECKING,
    GRANTED,
    DENIED,
    RATIONALE
}
