package com.example.photofilter.ui.state

import com.example.photofilter.domain.model.EditTab
import com.example.photofilter.domain.model.ImageData

/**
 * UI State cho EditScreen (MVVM)
 * 
 * Chứa tất cả trạng thái UI cần thiết cho EditScreen
 */
data class EditUiState(
    // ==================== DOMAIN STATE ====================
    val imageData: ImageData? = null,
    val isLoading: Boolean = false,
    val isSaving: Boolean = false,
    val errorMessage: String? = null,
    val statusMessage: String = "Chưa chọn ảnh nào",
    val hasUnsavedChanges: Boolean = false,
    
    // ==================== UI STATE ====================
    val selectedTab: EditTab = EditTab.TRANSFORM,
    val showExitDialog: Boolean = false,
    val shouldExitAfterSave: Boolean = false,
    val shouldExit: Boolean = false
)
