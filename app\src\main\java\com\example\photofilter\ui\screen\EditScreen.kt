package com.example.photofilter.ui.screen

import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Scaffold
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.onGloballyPositioned
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.unit.IntSize
import androidx.compose.ui.unit.dp
import com.example.photofilter.ui.action.EditScreenAction
import com.example.photofilter.ui.component.EditScreen.EditScreenExitDialog
import com.example.photofilter.ui.component.EditScreen.EditScreenImageDisplay
import com.example.photofilter.ui.component.EditScreen.EditScreenTabNavigation
import com.example.photofilter.ui.component.EditScreen.EditScreenToolsArea
import com.example.photofilter.ui.component.EditScreen.EditScreenTopBar
import com.example.photofilter.ui.effect.EditScreenBackHandler
import com.example.photofilter.ui.effect.EditScreenErrorEffect
import com.example.photofilter.ui.effect.EditScreenExitEffect
import com.example.photofilter.ui.effect.EditScreenSaveCompletionEffect
import com.example.photofilter.ui.effect.EditScreenSaveSuccessEffect
import com.example.photofilter.ui.viewmodel.EditViewModel

/**
 * Main EditScreen composable following MVVM architecture
 * This screen is now a pure container that delegates all logic to ViewModels and Components
 */
@Composable
fun EditScreen(
    onBack: () -> Unit,
    editViewModel: EditViewModel,
    modifier: Modifier = Modifier
) {
    var boxSize by remember { mutableStateOf(IntSize.Zero) }
    val context = LocalContext.current
    val uiState by editViewModel.uiState.collectAsState()
    var previousIsSaving by remember { mutableStateOf(false) }
    
    // Image picker launcher
    val imagePickerLauncher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.GetContent()
    ) { uri ->
        uri?.let {
            editViewModel.handleAction(EditScreenAction.LoadImage(it, context))
        }
    }

    // Exit after save callback
    val exitAfterSaveCallback = remember {
        {
            editViewModel.handleAction(EditScreenAction.ClearImageData)
            onBack()
        }
    }

    // Handle system back button
    EditScreenBackHandler {
        editViewModel.handleAction(EditScreenAction.RequestExit)
    }

    // Handle exit requests (when no image or after clearing image data)
    EditScreenExitEffect(
        shouldExit = uiState.shouldExit,
        onExit = {
            editViewModel.handleAction(EditScreenAction.ResetExitFlag)
            onBack()
        }
    )

    // Show error messages
    EditScreenErrorEffect(
        errorMessage = uiState.errorMessage,
        onErrorShown = { editViewModel.handleAction(EditScreenAction.ClearError) }
    )

    // Show success messages and handle exit after save
    EditScreenSaveSuccessEffect(
        statusMessage = uiState.statusMessage,
        shouldExitAfterSave = uiState.shouldExitAfterSave,
        onSuccess = { /* handled in effect */ },
        onExit = exitAfterSaveCallback,
        onStatusCleared = { editViewModel.handleAction(EditScreenAction.ClearStatusMessage) }
    )

    // Alternative method: Watch for save completion
    EditScreenSaveCompletionEffect(
        isSaving = uiState.isSaving,
        previousIsSaving = previousIsSaving,
        statusMessage = uiState.statusMessage,
        shouldExitAfterSave = uiState.shouldExitAfterSave,
        onExit = exitAfterSaveCallback,
        onPreviousSavingUpdated = { previousIsSaving = it }
    )

    // Main UI
    Scaffold(
        containerColor = Color.Transparent,
        topBar = {
            EditScreenTopBar(
                hasImage = uiState.imageData != null,
                isLoading = uiState.isLoading,
                isSaving = uiState.isSaving,
                onBack = { editViewModel.handleAction(EditScreenAction.RequestExit) },
                onReset = { editViewModel.handleAction(EditScreenAction.ResetToOriginal) },
                onSave = { editViewModel.handleAction(EditScreenAction.SaveImage(context)) }
            )
        }
    ) { paddingValues ->
        Box(
            modifier = modifier
                .fillMaxSize()
                .onGloballyPositioned { coordinates ->
                    boxSize = coordinates.size
                }
        ) {
            Box(
                modifier = Modifier
                    .matchParentSize()
                    .background(
                        Brush.radialGradient(
                            colors = listOf(Color(0x80FF2E97), Color.Transparent),
                            center = Offset(boxSize.width*0.2f, boxSize.height*0.5f),
                            radius = 800f
                        )
                    )
            )

            Box(
                modifier = Modifier
                    .matchParentSize()
                    .background(
                        Brush.radialGradient(
                            colors = listOf(Color(0x80A855FF), Color.Transparent),
                            center = Offset(boxSize.width*0.5f, boxSize.height*0.3f),
                            radius = 600f
                        )
                    )
            )

            Box(
                modifier = Modifier
                    .matchParentSize()
                    .background(
                        Brush.radialGradient(
                            colors = listOf(Color(0x8044C2FF), Color.Transparent),
                            center = Offset(boxSize.width*0.8f, boxSize.height*0.7f),
                            radius = 800f
                        )
                    )
            )
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(paddingValues)
            ) {
                // Image Display Area - giới hạn chiều cao
                EditScreenImageDisplay(
                    bitmap = uiState.imageData?.currentBitmap,
                    isLoading = uiState.isLoading,
                    imagePickerLauncher = imagePickerLauncher,
                    modifier = Modifier.weight(1f)
                )

                // Show tools area only when image is loaded - Height cố định
                uiState.imageData?.let { imageData ->
                    Box(
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(140.dp) // Height cố định để không bị đẩy xuống
                    ) {
                        EditScreenToolsArea(
                            selectedTab = uiState.selectedTab,
                            imageData = imageData,
                            isLoading = uiState.isLoading,
                            editViewModel = editViewModel,
                            onRotate90 = {
                                editViewModel.handleAction(
                                    EditScreenAction.RotateImage(
                                        90f
                                    )
                                )
                            },
                            onFlipHorizontal = { editViewModel.handleAction(EditScreenAction.FlipHorizontal) },
                            onFilterSelected = { filter ->
                                editViewModel.handleAction(EditScreenAction.ApplyFilter(filter))
                            },
                            onAdjustmentChange = { adjustments ->
                                editViewModel.handleAction(
                                    EditScreenAction.UpdateAdjustments(
                                        adjustments
                                    )
                                )
                            },
                            onAspectRatioSelected = { aspectRatio ->
                                editViewModel.handleAction(
                                    EditScreenAction.ApplyCropAspectRatio(
                                        aspectRatio
                                    )
                                )
                            }
                        )
                    }

                    Box(
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(80.dp) // Height cố định
                    ) {
                        EditScreenTabNavigation(
                            selectedTab = uiState.selectedTab,
                            onTabSelected = { tab ->
                                editViewModel.handleAction(EditScreenAction.SelectTab(tab))
                            }
                        )
                    }
                }
            }
        }
    }

    // Exit confirmation dialog
    EditScreenExitDialog(
        isVisible = uiState.showExitDialog,
        isSaving = uiState.isSaving,
        onDismiss = { editViewModel.handleAction(EditScreenAction.HideExitDialog) },
        onDiscardAndExit = {
            editViewModel.handleAction(EditScreenAction.HideExitDialog)
            editViewModel.handleAction(EditScreenAction.ClearImageData)
            onBack()
        },
        onSaveAndExit = {
            editViewModel.handleAction(EditScreenAction.SaveAndExit)
            editViewModel.handleAction(EditScreenAction.SaveImage(context))
        }
    )
}
