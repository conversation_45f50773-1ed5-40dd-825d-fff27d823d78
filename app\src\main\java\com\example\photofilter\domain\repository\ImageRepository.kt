package com.example.photofilter.domain.repository

import android.content.ContentResolver
import android.content.Context
import android.graphics.Bitmap
import android.net.Uri
import com.example.photofilter.domain.model.FilterData
import com.example.photofilter.domain.model.FilterType
import com.example.photofilter.domain.model.ImageData
import com.example.photofilter.utils.ImageProcessors

interface ImageRepository {
    val imageProcessors: ImageProcessors
    suspend fun loadImageFromUri(uri: Uri, contentResolver: ContentResolver): Result<Bitmap>
    suspend fun saveImageToGallery(context: Context, bitmap: Bitmap, fileName: String = ""): Result<Boolean>
    suspend fun applyFilter(imageData: ImageData, filterType: FilterType): Result<ImageData>
    suspend fun rotateImage(imageData: ImageData, angle: Float): Result<ImageData>
    suspend fun flipHorizontal(imageData: ImageData): Result<ImageData>
    suspend fun updateSliderRotation(imageData: ImageData, rotation: Float): Result<ImageData>
    suspend fun resetToOriginal(imageData: ImageData): Result<ImageData>
    
    // Thêm các methods mới
    suspend fun loadOptimizedImageFromUri(uri: Uri, contentResolver: ContentResolver): Result<ImageData>
    suspend fun getBaseTransformedBitmap(imageData: ImageData): Result<Bitmap>
    suspend fun applyAdjustments(imageData: ImageData, brightness: Float, contrast: Float, saturation: Float, blur: Float, sharpen: Float): Result<ImageData>
    suspend fun applyCropAspectRatio(imageData: ImageData, aspectRatio: Float): Result<ImageData>
}
