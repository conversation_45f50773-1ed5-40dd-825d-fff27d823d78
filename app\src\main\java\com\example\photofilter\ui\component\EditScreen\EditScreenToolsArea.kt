package com.example.photofilter.ui.component.EditScreen

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import com.example.photofilter.domain.model.AdjustmentData
import com.example.photofilter.domain.model.AspectRatio
import com.example.photofilter.domain.model.EditTab
import com.example.photofilter.domain.model.FilterType
import com.example.photofilter.ui.component.EditTools.AdjustmentTools
import com.example.photofilter.ui.component.EditTools.CropTools
import com.example.photofilter.ui.component.EditTools.FilterTools
import com.example.photofilter.ui.component.EditTools.TransformTools
import com.example.photofilter.ui.viewmodel.EditViewModel

/**
 * Tools area component for EditScreen
 * Manages different tool displays based on selected tab
 */
@Composable
fun EditScreenToolsArea(
    selectedTab: EditTab,
    imageData: com.example.photofilter.domain.model.ImageData,
    isLoading: <PERSON><PERSON><PERSON>,
    editViewModel: EditViewModel,
    onRotate90: () -> Unit,
    onFlipHorizontal: () -> Unit,
    onFilterSelected: (FilterType) -> Unit,
    onAdjustmentChange: (AdjustmentData) -> Unit,
    onAspectRatioSelected: (AspectRatio) -> Unit,
    modifier: Modifier = Modifier
) {
    Box(
        modifier = modifier
            .fillMaxWidth()
            .padding(8.dp),
        contentAlignment = Alignment.Center
    ) {
        when (selectedTab) {
            EditTab.TRANSFORM -> TransformTools(
                onRotate90 = onRotate90,
                onFlipHorizontal = onFlipHorizontal,
                enabled = !isLoading
            )
            EditTab.FILTER -> FilterTools(
                onFilterSelected = onFilterSelected,
                selectedFilter = imageData.filterData.appliedFilter,
                transformedBitmap = editViewModel.getTransformedBitmapWithoutFilter(),
                viewModel = editViewModel,
                enabled = !isLoading
            )
            EditTab.ADJUST -> AdjustmentTools(
                adjustmentData = imageData.adjustmentData,
                onAdjustmentChange = onAdjustmentChange,
                enabled = !isLoading
            )
            EditTab.CROP -> CropTools(
                cropData = imageData.cropData,
                onAspectRatioSelected = onAspectRatioSelected,
                enabled = !isLoading
            )
        }
    }
}
