package com.example.photofilter.ui.component.CollageScreen

import android.graphics.Bitmap
import androidx.activity.compose.ManagedActivityResultLauncher
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.height
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import com.example.photofilter.domain.model.CollageLayout

@Composable
fun CollageScreenToolsArea(
    images: List<Bitmap>,
    selectedLayout: CollageLayout?,
    isLoading: Boolean,
    imagePickerLauncher: ManagedActivityResultLauncher<String, List<android.net.Uri>>,
    onLayoutSelected: (CollageLayout) -> Unit,
    modifier: Modifier = Modifier
) {
    if (images.isNotEmpty()) {
        Column(modifier = modifier) {
            // Layout Selection
            CollageScreenLayoutSelector(
                selectedLayout = selectedLayout,
                imagesCount = images.size,
                onLayoutSelected = onLayoutSelected
            )

            Spacer(modifier = Modifier.height(8.dp))

            // Selected Images Preview
            CollageScreenImagesPreview(
                images = images,
                isLoading = isLoading,
                imagePickerLauncher = imagePickerLauncher
            )
        }
    }
}
