package com.example.photofilter.ui.component.EditTools

import android.graphics.Bitmap
import androidx.compose.foundation.Image
import androidx.compose.foundation.horizontalScroll
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Image
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import com.example.photofilter.domain.model.FilterType
import com.example.photofilter.ui.viewmodel.EditViewModel
import com.example.photofilter.utils.OptimizedLaunchedEffect
import com.example.photofilter.utils.rememberStableBitmap

@Composable
fun FilterTools(
    onFilterSelected: (FilterType) -> Unit,
    selectedFilter: FilterType,
    transformedBitmap: Bitmap? = null,
    viewModel: EditViewModel,
    enabled: Boolean,
) {
    Column(
        modifier = Modifier.fillMaxSize(),
        verticalArrangement = Arrangement.Center
    ) {
        // Filter name display

        Text(
            text = selectedFilter.displayName,
            style = MaterialTheme.typography.labelMedium,
            modifier = Modifier.fillMaxWidth(),
            textAlign = TextAlign.Center
        )

        Spacer(modifier = Modifier.height(12.dp))
        // All Filters and Effects in one row (fixed)
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .horizontalScroll(rememberScrollState()),
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            FilterType.entries.forEach { filterItem ->
                FilterButton(
                    onClick = { onFilterSelected(filterItem) },
                    selected = (filterItem == selectedFilter),
                    filterType = filterItem,
                    enabled = enabled,
                    previewBitmap = transformedBitmap,
                    viewModel = viewModel
                )
            }
        }
    }
}

@Composable
private fun FilterButton(
    onClick: () -> Unit,
    selected: Boolean,
    filterType: FilterType,
    enabled: Boolean,
    previewBitmap: Bitmap?,
    viewModel: EditViewModel,
    modifier: Modifier = Modifier
) {
    Card(
        onClick = onClick,
        modifier = modifier
            .width(70.dp)
            .height(70.dp),
        colors = CardDefaults.cardColors(
            containerColor = if (selected) MaterialTheme.colorScheme.onSurface.copy(0.7f) else Color.Transparent,
            contentColor = MaterialTheme.colorScheme.onSurface
        ),
        shape = MaterialTheme.shapes.small,
        enabled = enabled
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(2.dp),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.Center
        ) {
            previewBitmap?.let { bitmap ->
                // Create a smaller preview bitmap for better performance
                val previewBitmap = remember(bitmap) {
                    val maxSize = 100 // Small size for preview
                    val ratio = minOf(
                        maxSize.toFloat() / bitmap.width,
                        maxSize.toFloat() / bitmap.height
                    )

                    if (ratio >= 1f) bitmap
                    else {
                        val newWidth = (bitmap.width * ratio).toInt()
                        val newHeight = (bitmap.height * ratio).toInt()
                        Bitmap.createScaledBitmap(bitmap, newWidth, newHeight, true)
                    }
                }

                // Apply filter using viewModel with optimized preview
                var filteredPreviewBitmap by remember(previewBitmap, filterType) {
                    mutableStateOf(previewBitmap)
                }

                val shouldExitAfterSave = false
                OptimizedLaunchedEffect(previewBitmap to filterType, shouldExitAfterSave) {
                    filteredPreviewBitmap =
                        viewModel.applyFilterToPreview(previewBitmap, filterType)
                }

                val stableFilteredBitmap = rememberStableBitmap(filteredPreviewBitmap)
                stableFilteredBitmap?.let { stable ->
                    Image(
                        bitmap = stable.imageBitmap,
                        contentDescription = null,
                        modifier = Modifier
                            .fillMaxSize()
                            .clip(RoundedCornerShape(8.dp)),
                        contentScale = ContentScale.Crop
                    )
                }
            } ?: run {
                // Fallback icon if no image
                Icon(
                    imageVector = Icons.Default.Image,
                    contentDescription = null,
                    modifier = Modifier.size(24.dp),
                    tint = MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.6f)
                )
            }
        }
    }
}