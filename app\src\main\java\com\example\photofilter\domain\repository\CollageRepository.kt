package com.example.photofilter.domain.repository

import android.content.ContentResolver
import android.content.Context
import android.graphics.Bitmap
import android.net.Uri
import com.example.photofilter.domain.model.CollageData
import com.example.photofilter.domain.model.CollageLayout

interface CollageRepository {
    suspend fun loadImages(uris: List<Uri>, contentResolver: ContentResolver): Result<List<Bitmap>>
    suspend fun createCollage(images: List<Bitmap>, layout: CollageLayout): Result<CollageData>
    suspend fun saveCollage(context: Context, collageData: CollageData, fileName: String): Result<Unit>
}
