package com.example.photofilter.domain.model

import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Ballot
import androidx.compose.material.icons.filled.BlurCircular
import androidx.compose.material.icons.filled.Brightness6
import androidx.compose.material.icons.filled.Contrast
import androidx.compose.material.icons.filled.SettingsBrightness
import androidx.compose.ui.graphics.vector.ImageVector

data class AdjustmentData(
    var brightness: Float = DEFAULT_BRIGHTNESS,
    var contrast: Float = DEFAULT_CONTRAST,
    var saturation: Float = DEFAULT_SATURATION,
    var blur: Float = DEFAULT_BLUR,
    var sharpen: Float = DEFAULT_SHARPEN,
) {
    companion object {
        const val DEFAULT_BRIGHTNESS = 0f
        const val DEFAULT_CONTRAST = 1f
        const val DEFAULT_SATURATION = 1f
        const val DEFAULT_BLUR = 0f
        const val DEFAULT_SHARPEN = 0f
    }

    fun isDefault(): Boolean {
        return brightness == DEFAULT_BRIGHTNESS &&
                contrast == DEFAULT_CONTRAST &&
                saturation == DEFAULT_SATURATION &&
                blur == DEFAULT_BLUR &&
                sharpen == DEFAULT_SHARPEN
    }

    fun resetAdjustment(adjustmentType: AdjustmentType): AdjustmentData {
        return when(adjustmentType) {
            AdjustmentType.BRIGHTNESS -> copy(brightness = DEFAULT_BRIGHTNESS)
            AdjustmentType.CONTRAST -> copy(contrast = DEFAULT_CONTRAST)
            AdjustmentType.SATURATION -> copy(saturation = DEFAULT_SATURATION)
            AdjustmentType.BLUR -> copy(blur = DEFAULT_BLUR)
            AdjustmentType.SHARPEN -> copy(sharpen = DEFAULT_SHARPEN)
        }
    }

    fun isAtDefault(adjustmentType: AdjustmentType): Boolean {
        return when(adjustmentType) {
            AdjustmentType.BRIGHTNESS -> brightness == DEFAULT_BRIGHTNESS
            AdjustmentType.CONTRAST -> contrast == DEFAULT_CONTRAST
            AdjustmentType.SATURATION -> saturation == DEFAULT_SATURATION
            AdjustmentType.BLUR -> blur == DEFAULT_BLUR
            AdjustmentType.SHARPEN -> sharpen == DEFAULT_SHARPEN
        }
    }

    fun getAdjustmentValue(adjustmentType: AdjustmentType): Float {
        return when(adjustmentType) {
            AdjustmentType.BRIGHTNESS -> brightness
            AdjustmentType.CONTRAST -> contrast
            AdjustmentType.SATURATION -> saturation
            AdjustmentType.BLUR -> blur
            AdjustmentType.SHARPEN -> sharpen
        }
    }

    fun updateAdjustment(adjustmentType: AdjustmentType, value: Float): AdjustmentData {
        return when(adjustmentType) {
            AdjustmentType.BRIGHTNESS -> copy(brightness = value)
            AdjustmentType.CONTRAST -> copy(contrast = value)
            AdjustmentType.SATURATION -> copy(saturation = value)
            AdjustmentType.BLUR -> copy(blur = value)
            AdjustmentType.SHARPEN -> copy(sharpen = value)
        }
    }
}

enum class AdjustmentType(
    val displayName: String,
    val icon: ImageVector,
    val range: ClosedFloatingPointRange<Float>,
    val steps: Int
) {
    BRIGHTNESS("Độ sáng", Icons.Default.Brightness6, -100f..100f, 199),
    CONTRAST("Tương phản", Icons.Default.Contrast, 0.5f..2.0f, 14),
    SATURATION("Độ bão hòa", Icons.Default.Ballot, 0.0f..2.0f, 19),
    BLUR("Làm mờ", Icons.Default.BlurCircular, 0f..25f, 24),
    SHARPEN("Làm sắc nét", Icons.Default.SettingsBrightness, 0f..2.0f, 19)
}

