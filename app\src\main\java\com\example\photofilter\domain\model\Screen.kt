package com.example.photofilter.domain.model

import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Edit
import androidx.compose.material.icons.filled.GridView
import androidx.compose.material.icons.filled.Home
import androidx.compose.ui.graphics.vector.ImageVector

enum class Screen(val icon: ImageVector) {
    HOME(Icons.Default.Home),
    EDIT(Icons.Default.Edit),
    COLLAGE(Icons.Default.GridView)
}
