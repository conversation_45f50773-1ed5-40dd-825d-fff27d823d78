<resources xmlns:tools="http://schemas.android.com/tools">
    <!-- Base application theme. -->
    <style name="Base.Theme.PhotoFilter" parent="Theme.Material3.DayNight.NoActionBar">
        <!-- Customize your light theme here. -->
        <item name="windowSplashScreenBackground">@color/transparent</item>
        <item name="windowSplashScreenAnimatedIcon">@drawable/transparent</item>
<!--        <item name="postSplashScreenTheme">@style/AppTheme</item>-->
        <!-- Status bar colors -->
        <item name="android:statusBarColor">@color/ic_launcher_background</item>
        <item name="android:windowLightStatusBar">false</item>

<!--        &lt;!&ndash; Background color &ndash;&gt;-->
<!--        <item name="android:windowBackground">@drawable/splash_background</item>-->
<!--&lt;!&ndash;        <item name="android:windowBackground">@color/ic_launcher_background</item>&ndash;&gt;-->

<!--        &lt;!&ndash; Material 3 Primary colors &ndash;&gt;-->
<!--        <item name="colorPrimary">@color/splash_brand_color</item>-->
<!--        <item name="colorOnPrimary">@color/white</item>-->
<!--        <item name="colorPrimaryContainer">@color/splash_accent</item>-->
<!--        <item name="colorOnPrimaryContainer">@color/white</item>-->

<!--        &lt;!&ndash; Material 3 Secondary colors &ndash;&gt;-->
<!--        <item name="colorSecondary">@color/splash_accent</item>-->
<!--        <item name="colorOnSecondary">@color/white</item>-->
<!--        <item name="colorSecondaryContainer">@color/splash_background</item>-->
<!--        <item name="colorOnSecondaryContainer">@color/white</item>-->

<!--        &lt;!&ndash; Material 3 Surface colors &ndash;&gt;-->
<!--        <item name="colorSurface">@color/ic_launcher_background</item>-->
<!--        <item name="colorOnSurface">@color/white</item>-->
<!--        <item name="colorSurfaceVariant">@color/splash_background</item>-->

<!--        <item name="colorOnSurfaceVariant">@color/white</item>-->

<!--        &lt;!&ndash; Material 3 Background &ndash;&gt;-->
<!--        <item name="android:colorBackground">@color/ic_launcher_background</item>-->
<!--        <item name="colorOnBackground">@color/white</item>-->

        <item name="android:windowIsTranslucent">true</item>
    </style>

    <style name="Theme.PhotoFilter" parent="Base.Theme.PhotoFilter" />

    <style name="Theme.PhotoFilter.Splash" parent="Theme.PhotoFilter">
        <item name="android:windowBackground">@drawable/splash_background</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowFullscreen">true</item>
        <item name="android:windowContentOverlay">@null</item>
    </style>

</resources>