package com.example.photofilter.ui.effect

import android.widget.Toast
import androidx.activity.compose.BackHandler
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.platform.LocalContext
import android.util.Log
import kotlinx.coroutines.delay

/**
 * Side effects for EditScreen
 * Separated for better organization and testability
 */

@Composable
fun EditScreenBackHandler(
    onBackPressed: () -> Unit
) {
    BackHandler {
        onBackPressed()
    }
}

@Composable
fun EditScreenErrorEffect(
    errorMessage: String?,
    onErrorShown: () -> Unit
) {
    val context = LocalContext.current
    
    LaunchedEffect(errorMessage) {
        errorMessage?.let { error ->
            Toast.makeText(context, error, Toast.LENGTH_SHORT).show()
            onErrorShown()
        }
    }
}

@Composable
fun EditScreenSaveSuccessEffect(
    statusMessage: String,
    shouldExitAfterSave: <PERSON><PERSON>an,
    onSuccess: () -> Unit,
    onExit: () -> Unit,
    onStatusCleared: () -> Unit
) {
    val context = LocalContext.current
    
    LaunchedEffect(statusMessage, shouldExitAfterSave) {
        Log.d("EditScreen", "StatusMessage: '$statusMessage', ShouldExit: $shouldExitAfterSave")
        if (statusMessage == "Đã lưu ảnh thành công") {
            Toast.makeText(context, "Đã lưu ảnh thành công!", Toast.LENGTH_LONG).show()
            onSuccess()
            
            // Exit after save if requested
            if (shouldExitAfterSave) {
                Log.d("EditScreen", "Exiting after save...")
                delay(100) // Small delay to ensure UI updates
                onExit()
                return@LaunchedEffect
            }
            
            onStatusCleared()
        }
    }
}

@Composable
fun EditScreenSaveCompletionEffect(
    isSaving: Boolean,
    previousIsSaving: Boolean,
    statusMessage: String,
    shouldExitAfterSave: Boolean,
    onExit: () -> Unit,
    onPreviousSavingUpdated: (Boolean) -> Unit
) {
    LaunchedEffect(isSaving, statusMessage) {
        Log.d("EditScreen", "isSaving: $isSaving, statusMessage: '$statusMessage', shouldExit: $shouldExitAfterSave")
        
        // If saving just completed and we should exit
        if (!isSaving && shouldExitAfterSave && 
            (statusMessage == "Đã lưu ảnh thành công" || previousIsSaving)) {
            Log.d("EditScreen", "Alternative exit method triggered")
            delay(200)
            onExit()
        }
        
        onPreviousSavingUpdated(isSaving)
    }
}

@Composable
fun EditScreenExitEffect(
    shouldExit: Boolean,
    onExit: () -> Unit
) {
    LaunchedEffect(shouldExit) {
        if (shouldExit) {
            Log.d("EditScreen", "Exit effect triggered")
            onExit()
        }
    }
}
