package com.example.photofilter.utils

import android.Manifest
import android.app.Activity
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.net.Uri
import android.os.Build
import android.provider.Settings
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat

/**
 * Bộ tiện ích quản lý quyền truy cập (Runtime Permissions)
 * 
 * === MỤC ĐÍCH CHÍNH ===
 * - Quản lý quyền truy cập ảnh/bộ nhớ theo từng phiên bản Android
 * - <PERSON><PERSON><PERSON> tra trạng thái quyền hiện tại
 * - <PERSON><PERSON> cấp helper functions cho permission workflow
 * - Xử lý sự khác biệt giữa các Android API levels
 * 
 * === ANDROID PERMISSION EVOLUTION ===
 * Android ≤ 9 (API 28):
 * - Cần READ_EXTERNAL_STORAGE + WRITE_EXTERNAL_STORAGE
 * - Full access vào external storage
 * 
 * Android 10-12 (API 29-32):
 * - Scoped Storage được introduce
 * - Chỉ cần READ_EXTERNAL_STORAGE
 * - WRITE_EXTERNAL_STORAGE không cần cho MediaStore
 * 
 * Android 13+ (API 33+):
 * - Granular media permissions
 * - READ_MEDIA_IMAGES thay thế READ_EXTERNAL_STORAGE
 * - Riêng biệt cho images, videos, audio
 */

// Unified object từ PermissionUtils và CollagePermissionUtils
object PermissionUtils {

    // ==================== PERMISSION STATUS CHECK ====================

    /**
     * Kiểm tra xem app có đủ tất cả quyền cần thiết không
     * 
     * @param context: Android Context
     * @return: true nếu có đủ quyền, false nếu thiếu ít nhất 1 quyền
     * 
     * === CÁCH HOẠT ĐỘNG ===
     * 1. Lấy danh sách quyền cần thiết theo API level
     * 2. Kiểm tra từng quyền với ContextCompat.checkSelfPermission()
     * 3. Trả về true chỉ khi TẤT CẢ quyền đều GRANTED
     * 
     * === USE CASES ===
     * - Kiểm tra trước khi mở gallery picker
     * - Validate trong onResume() sau khi user quay lại từ Settings
     * - Conditional UI display based on permissions
     */
    fun hasPermissions(context: Context): Boolean {
        return getRequiredPermissions().all { permission ->
            ContextCompat.checkSelfPermission(context, permission) == PackageManager.PERMISSION_GRANTED
        }
    }

    /**
     * Kiểm tra có nên hiển thị rationale (giải thích tại sao cần quyền) không
     * 
     * @param activity: Activity để check rationale
     * @return: true nếu nên show rationale cho ít nhất 1 quyền
     * 
     * === KHI NÀO SHOW RATIONALE ===
     * - User đã deny permission ít nhất 1 lần
     * - User chưa chọn "Don't ask again"
     * - Android khuyến nghị show explanation trước khi request
     * 
     * === KHI NÀO KHÔNG SHOW ===
     * - Lần đầu tiên request permission
     * - User đã chọn "Don't ask again" (permanently denied)
     * - Permission đã được granted
     */
    fun shouldShowRationale(activity: Activity): Boolean {
        return getRequiredPermissions().any { permission ->
            ActivityCompat.shouldShowRequestPermissionRationale(activity, permission)
        }
    }

    /**
     * Mở trang cài đặt ứng dụng để user cấp quyền thủ công
     * 
     * @param context: Android Context
     *
     * === CÁCH HOẠT ĐỘNG ===
     * - Tạo Intent với ACTION_APPLICATION_DETAILS_SETTINGS
     * - Navigate trực tiếp đến app settings page
     * - User có thể toggle permissions manually
     */
    fun openAppSettings(context: Context) {
        val intent = Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS).apply {
            data = Uri.fromParts("package", context.packageName, null)
        }
        context.startActivity(intent)
    }
    
    // Check if app has storage/media read permission
    fun hasStoragePermission(context: Context): Boolean {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            ContextCompat.checkSelfPermission(
                context,
                Manifest.permission.READ_MEDIA_IMAGES
            ) == PackageManager.PERMISSION_GRANTED
        } else {
            ContextCompat.checkSelfPermission(
                context,
                Manifest.permission.READ_EXTERNAL_STORAGE
            ) == PackageManager.PERMISSION_GRANTED
        }
    }
    
    // Check if app has write permission
    fun hasWritePermission(context: Context): Boolean {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            // Android 10+ doesn't need WRITE_EXTERNAL_STORAGE for MediaStore
            true
        } else {
            ContextCompat.checkSelfPermission(
                context,
                Manifest.permission.WRITE_EXTERNAL_STORAGE
            ) == PackageManager.PERMISSION_GRANTED
        }
    }

    // Get all required permissions based on Android version
    fun getRequiredPermissions(): Array<String> {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            // Android 13+
            arrayOf(Manifest.permission.READ_MEDIA_IMAGES)
        } else if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            // Android 10-12
            arrayOf(Manifest.permission.READ_EXTERNAL_STORAGE)
        } else {
            // > Android 9
            arrayOf(
                Manifest.permission.READ_EXTERNAL_STORAGE,
                Manifest.permission.WRITE_EXTERNAL_STORAGE
            )
        }
    }

    // Get missing permissions that need to be requested
    fun getMissingPermissions(context: Context): List<String> {
        return getRequiredPermissions().filter { permission ->
            ContextCompat.checkSelfPermission(context, permission) != PackageManager.PERMISSION_GRANTED
        }
    }

    // Get user-friendly display name for permission
    fun getPermissionDisplayName(permission: String): String {
        return when (permission) {
            Manifest.permission.READ_EXTERNAL_STORAGE -> "Đọc bộ nhớ"
            Manifest.permission.WRITE_EXTERNAL_STORAGE -> "Ghi bộ nhớ"
            Manifest.permission.READ_MEDIA_IMAGES -> "Truy cập ảnh"
            else -> "Quyền không xác định"
        }
    }
    
    // Get explanation message for permission request
    fun getPermissionExplanation(): String {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            "Ứng dụng cần quyền truy cập ảnh để:\n" +
            "• Tải ảnh từ thư viện\n" +
            "• Tạo và lưu collage\n" +
            "• Áp dụng filter và chỉnh sửa ảnh"
        } else {
            "Ứng dụng cần quyền truy cập bộ nhớ để:\n" +
            "• Tải ảnh từ thư viện\n" +
            "• Tạo và lưu collage\n" +
            "• Áp dụng filter và chỉnh sửa ảnh"
        }
    }

     // Alias for hasStoragePermission - more descriptive
    fun canReadImages(context: Context): Boolean = hasStoragePermission(context)
    
    // Alias for hasWritePermission - more descriptive
    fun canSaveImages(context: Context): Boolean = hasWritePermission(context)
}
