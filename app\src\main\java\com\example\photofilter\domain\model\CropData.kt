package com.example.photofilter.domain.model

data class CropData(
    val x: Int = 0,
    val y: Int = 0,
    val width: Int = 0,
    val height: Int = 0,
    val aspectRatio: Float? = 0f,
) {
    fun isDefault(): Boolean {
        return x == 0 && y == 0 && width == 0 && height == 0 && aspectRatio == 0f
    }
}

enum class AspectRatio(
    val ratio: Float,
    val displayName: String
) {
    FREE(0f, "Gốc"),
    SQUARE(1f, "1:1"),
    PORTRAIT_3_4(3f/4f, "3:4"),
    PORTRAIT_9_16(9f/16f, "9:16"),
    LANDSCAPE_4_3(4f/3f, "4:3"),
    LANDSCAPE_16_9(16f/9f, "16:9")
}

