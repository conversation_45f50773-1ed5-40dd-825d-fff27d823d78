package com.example.photofilter.ui.effect

import android.widget.Toast
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.platform.LocalContext

@Composable
fun CollageScreenErrorEffect(
    errorMessage: String?,
    onErrorShown: () -> Unit
) {
    val context = LocalContext.current
    
    LaunchedEffect(errorMessage) {
        errorMessage?.let { error ->
            Toast.makeText(context, error, Toast.LENGTH_SHORT).show()
            onErrorShown()
        }
    }
}
