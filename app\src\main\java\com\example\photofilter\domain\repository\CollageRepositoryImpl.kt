package com.example.photofilter.domain.repository

import android.content.ContentResolver
import android.content.Context
import android.graphics.Bitmap
import android.net.Uri
import com.example.photofilter.domain.model.CollageData
import com.example.photofilter.domain.model.CollageLayout
import com.example.photofilter.domain.repository.CollageRepository
import com.example.photofilter.utils.CollageUtils
import com.example.photofilter.utils.ImageUtils
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

class CollageRepositoryImpl : CollageRepository {

    override suspend fun loadImages(
        uris: List<Uri>,
        contentResolver: ContentResolver
    ): Result<List<Bitmap>> =
        withContext(Dispatchers.IO) {
            try {
                val bitmaps = mutableListOf<Bitmap>()
                for (uri in uris) {
                    val bitmap = ImageUtils.loadBitmapFromUri(uri, contentResolver)
                    if (bitmap != null) {
                        bitmaps.add(bitmap)
                    } else {
                        return@withContext Result.failure(Exception("Không thể tải ảnh từ: $uri"))
                    }
                }
                Result.success(bitmaps)
            } catch (e: OutOfMemoryError) {
                Result.failure(Exception("Không đủ bộ nhớ để tải ảnh. Hãy chọn ít ảnh hơn."))
            } catch (e: SecurityException) {
                Result.failure(Exception("Không có quyền truy cập ảnh. Vui lòng cấp quyền."))
            } catch (e: Exception) {
                Result.failure(Exception("Lỗi tải ảnh: ${e.message}"))
            }
        }

    override suspend fun createCollage(
        images: List<Bitmap>,
        layout: CollageLayout
    ): Result<CollageData> =
        withContext(Dispatchers.Default) {
            try {
                if (images.isEmpty()) {
                    return@withContext Result.failure(Exception("Không có ảnh nào để tạo collage"))
                }

                if (images.size > layout.maxImages) {
                    return@withContext Result.failure(Exception("Quá nhiều ảnh cho layout này"))
                }

                val collageBitmap = when (layout) {
                    CollageLayout.GRID_2X2 -> CollageUtils.createGrid2x2Collage(images)
                    CollageLayout.GRID_3X3 -> CollageUtils.createGrid3x3Collage(images)
                    CollageLayout.HORIZONTAL_2 -> CollageUtils.createHorizontalCollage(images, 2)
                    CollageLayout.VERTICAL_2 -> CollageUtils.createVerticalCollage(images, 2)
                    CollageLayout.HORIZONTAL_3 -> CollageUtils.createHorizontalCollage(images, 3)
                    CollageLayout.VERTICAL_3 -> CollageUtils.createVerticalCollage(images, 3)
                }

                val collageData = CollageData(
                    images = images,
                    layout = layout,
                    finalBitmap = collageBitmap
                )

                Result.success(collageData)
            } catch (e: Exception) {
                Result.failure(e)
            }
        }

    override suspend fun saveCollage(
        context: Context,
        collageData: CollageData,
        fileName: String
    ): Result<Unit> =
        withContext(Dispatchers.IO) {
            try {
                val bitmap = collageData.finalBitmap
                    ?: return@withContext Result.failure(Exception("Không có collage để lưu"))

                ImageUtils.saveBitmapToGallery(context, bitmap, fileName, "Collage")
                    .fold(
                        onSuccess = { Result.success(Unit) },
                        onFailure = { error -> Result.failure(error) }
                    )

            } catch (e: Exception) {
                Result.failure(e)
            }
        }
}

