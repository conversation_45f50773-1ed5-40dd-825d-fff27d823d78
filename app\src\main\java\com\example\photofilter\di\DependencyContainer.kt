package com.example.photofilter.di

import com.example.photofilter.domain.repository.CollageRepositoryImpl
import com.example.photofilter.domain.repository.ImageRepositoryImpl
import com.example.photofilter.domain.repository.CollageRepository
import com.example.photofilter.domain.repository.ImageRepository
import com.example.photofilter.utils.ImageCacheManager
import com.example.photofilter.utils.ImageProcessors

/**
 * DependencyContainer - Container chứa tất cả dependencies của app (Simple MVVM)
 * 
 * === MỤC ĐÍCH ===
 * - Cung cấp Dependency Injection thủ công cho MVVM pattern
 * - Quản lý lifecycle của các object singleton
 * - Tránh việc tạo object mới không cần thiết
 * 
 * === KIẾN TRÚC ===
 * - Simple MVVM: UI → ViewModel → Repository → Utils
 * - Không sử dụng Dagger/Hilt để đơn giản hóa
 * - Lazy initialization để tối ưu memory
 * 
 * === CÁCH SỬ DỤNG ===
 * ```
 * // Trong ViewModelFactory
 * EditViewModel(DependencyContainer.imageRepository)
 * 
 * // Clear cache khi cần
 * DependencyContainer.clearImageCache()
 * ```
 */
object DependencyContainer {
    
    // ==================== PRIVATE UTILITIES ====================
    
    /**
     * ImageProcessors - Xử lý các thao tác image processing
     * - Apply filters (Vintage, Black&White, Sepia, v.v.)
     * - Image transformations (resize, crop, rotate)
     * - Performance optimized với native code
     */
    private val imageProcessors: ImageProcessors by lazy { 
        ImageProcessors() 
    }
    
    /**
     * ImageCacheManager - Quản lý cache cho ảnh đã xử lý
     * - LRU Cache để tránh OOM
     * - Cache filter previews để tăng tốc UI
     * - Tự động clear khi memory thấp
     */
    private val imageCacheManager: ImageCacheManager by lazy { 
        ImageCacheManager() 
    }

    // ==================== REPOSITORIES (MVVM LAYER) ====================
    
    /**
     * ImageRepository - Repository chính cho tất cả thao tác ảnh
     * 
     * === CHỨC NĂNG CHÍNH ===
     * - Load ảnh từ URI với memory optimization
     * - Apply filters với cache support
     * - Transform operations (rotate, flip, crop)
     * - Save ảnh vào Gallery
     * - Adjustment operations (brightness, contrast, saturation)
     * 
     * === DEPENDENCIES ===
     * - ImageProcessors: Để xử lý ảnh
     * - ImageCacheManager: Để cache kết quả
     */
    val imageRepository: ImageRepository by lazy {
        ImageRepositoryImpl(imageProcessors)
    }

    /**
     * CollageRepository - Repository cho tính năng tạo collage
     * 
     * === CHỨC NĂNG CHÍNH ===
     * - Tạo collage từ nhiều ảnh
     * - Quản lý layouts khác nhau (2x2, 3x3, custom)
     * - Apply effects lên toàn bộ collage
     * - Export collage với quality cao
     * 
     * === SỬ DỤNG ===
     * - Trong CollageViewModel để xử lý logic collage
     * - Hỗ trợ async operations với Coroutines
     */
    val collageRepository: CollageRepository by lazy {
        CollageRepositoryImpl()
    }

    // ==================== UTILITY FUNCTIONS ====================
    
    /**
     * Clear toàn bộ image cache
     * 
     * === KHI NÀO SỬ DỤNG ===
     * - Khi user logout
     * - Khi app bị low memory warning
     * - Khi user manually clear cache trong settings
     * - Khi switch giữa nhiều projects
     * 
     * === LƯU Ý ===
     * - Cache sẽ được rebuild tự động khi cần
     * - Có thể gây chậm lần đầu load ảnh sau khi clear
     */
    fun clearImageCache() {
        imageCacheManager.clearCache()
    }
}
