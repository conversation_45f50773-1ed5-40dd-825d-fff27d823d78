package com.example.photofilter

import android.annotation.SuppressLint
import android.content.Intent
import android.os.Bundle
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.lifecycleScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

@SuppressLint("CustomSplashScreen")
class SplashActivity : AppCompatActivity() {
    
    private val splashTimeOut: Long = 2000 // 2 seconds
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        // Set a simple background color or you can create a splash layout
        setContentView(android.R.layout.activity_list_item)
        
        // Use coroutines instead of deprecated Handler
        lifecycleScope.launch {
            delay(splashTimeOut)
            navigateToMainActivity()
        }
    }
    
    private fun navigateToMainActivity() {
        try {
            val intent = Intent(this, MainActivity::class.java)
            startActivity(intent)
            finish()
        } catch (e: Exception) {
            // Handle any potential errors
            e.printStackTrace()
            finish()
        }
    }
}
