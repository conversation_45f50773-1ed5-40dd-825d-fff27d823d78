package com.example.photofilter.ui.theme

import androidx.compose.ui.graphics.Color

// Dark Theme Colors (Black & White with subtle grays)
val DarkPrimary = Color(0xFFFFFFFF)        // Pure white for primary elements
val DarkOnPrimary = Color(0xFF000000)     // Black text on white
val DarkSecondary = Color(0xFF808080)     // Medium gray for secondary elements
val DarkOnSecondary = Color(0xFFFFFFFF)   // White text on gray
val DarkTertiary = Color(0xFF404040)      // Dark gray for tertiary elements
val DarkOnTertiary = Color(0xFFFFFFFF)    // White text on dark gray

val DarkBackground = Color(0xFF201D25)     // Pure black background
val DarkOnBackground = Color(0xFFFFFFFF)  // White text on black background
val DarkSurface = Color(0xFF000000)       // Very dark gray for surfaces
val DarkOnSurface = Color(0xFFFFFFFF)     // White text on surfaces
val DarkSurfaceVariant = Color(0xFF1E1E1E) // Slightly lighter dark gray
val DarkOnSurfaceVariant = Color(0xFFE0E0E0) // Light gray text

// Light Theme Colors (Inverted for contrast)
val LightPrimary = Color(0xFF000000)      // Black for primary elements
val LightOnPrimary = Color(0xFFFFFFFF)    // White text on black
val LightSecondary = Color(0xFF606060)    // Dark gray for secondary elements
val LightOnSecondary = Color(0xFFFFFFFF)  // White text on dark gray
val LightTertiary = Color(0xFF404040)     // Dark gray for tertiary elements
val LightOnTertiary = Color(0xFFFFFFFF)   // White text on dark gray

val LightBackground = Color(0xFFC8C9E1)   // Pure white background
val LightOnBackground = Color(0xFFFFFFFF) // Black text on white background
val LightSurface = Color(0xFFFFFFFF)      // Very light gray for surfaces
val LightOnSurface = Color(0xFF212121)    // Black text on surfaces
val LightSurfaceVariant = Color(0xFFF0F0F0) // Light gray for variants
val LightOnSurfaceVariant = Color(0xFF404040) // Dark gray text