package com.example.photofilter.domain.model

data class FilterData(
    val appliedFilter: FilterType = FilterType.ORIGINAL,
    val parameterValues: Map<String, Float> = emptyMap()
) {
    fun isDefault(): Boolean {
        return appliedFilter == FilterType.ORIGINAL
    }
}

enum class FilterType(
    val displayName: String
) {
    // Basic Filters
    ORIGINAL("Ảnh gốc"),
    GRAYSCALE("Đen trắng"),
    SEPIA("Cổ điển"),
    COOL_FILTER("Lạnh"),
    WARM_FILTER("Ấm"),
    VINTAGE_FILTER("Vintage"),
    INVERT_COLORS("Đảo màu"),
}
