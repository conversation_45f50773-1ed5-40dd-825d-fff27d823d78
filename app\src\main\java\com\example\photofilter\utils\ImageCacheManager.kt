package com.example.photofilter.utils

import android.graphics.Bitmap
import android.util.LruCache
import com.example.photofilter.domain.model.FilterType
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

/**
 * === MỤC ĐÍCH CHÍNH ===
 * - Lưu trữ ảnh đã xử lý filter để tránh tính toán lại (tiết kiệm CPU)
 * - Lưu trữ ảnh preview để hiển thị nhanh trong UI (tăng trải nghiệm người dùng)
 * - Quản lý bộ nhớ thông minh với LRU (Least Recently Used) algorithm
 * - Tự động dọn dẹp bộ nhớ khi cần thiết để tránh OutOfMemoryError
 * 
 * === CÁCH THỨC HOẠT ĐỘNG ===
 * 1. Khi áp dụng filter lần đầu: T<PERSON>h toán → Lưu vào cache → Tr<PERSON> về kết quả
 * 2. Khi áp dụng filter đã có: L<PERSON>y từ cache → Tr<PERSON> về ngay lập tức (nhanh hơn 90%)
 * 3. Khi cache đầy: Tự động xóa ảnh ít sử dụng nhất (LRU)
 * 4. Khi thiếu bộ nhớ: Tự động giải phóng bitmap để tránh crash
 */
class ImageCacheManager {
    /**
    -Tính toán kích thước cache (sử dụng 1/8 bộ nhớ khả dụng,
      Đủ lớn để cache nhiều ảnh, nhưng không quá lớn gây thiếu RAM cho hệ thống)

    - Runtime.getRuntime().maxMemory(): Lấy tổng bộ nhớ heap tối đa của ứng dụng
    - Chia 1024: Chuyển từ bytes sang kilobytes để dễ quản lý
    */
    private val maxMemory = (Runtime.getRuntime().maxMemory() / 1024).toInt()
    private val cacheSize = maxMemory / 8

    // Tạo hash unique cho bitmap để làm key cache
    /**
     * @param bitmap: Bitmap cần tạo hash
     * @return: String hash bao gồm kích thước và hashcode của bitmap
     *
     * === THUẬT TOÁN HASH ===
     * - Kết hợp: width x height + hashCode của bitmap
     * - Đảm bảo mỗi ảnh khác nhau có hash khác nhau
     * - Đảm bảo cùng một ảnh luôn có cùng hash
     *
     * === TẠI SAO SỬ DỤNG COROUTINE ===
     * - hashCode() có thể chậm với bitmap lớn
     * - withContext(Dispatchers.Default): Chạy trong background thread
     * - Không block UI thread
     */
    suspend fun generateBitmapHash(bitmap: Bitmap): String = withContext(Dispatchers.Default) {
        "${bitmap.width}x${bitmap.height}_${bitmap.hashCode()}"
    }


    // Cache cho ảnh preview
    /**
     - Lưu trữ ảnh preview nhỏ (thường 200x200px) để hiển thị trong danh sách filter
     - Sử dụng toàn bộ kích thước cache đã tính toán (cacheSize)
     - Tự động xóa ảnh cũ nhất khi cache đầy (LRU strategy)
     */
    private val previewCache = object : LruCache<String, Bitmap>(cacheSize) {
        /**
         * Tính kích thước của mỗi item trong cache
         * 
         * @param key: Khóa định danh ảnh (String)
         * @param bitmap: Bitmap cần lưu
         * @return: Kích thước bitmap tính bằng KB
         * 
         * === CÁCH TÍNH ===
         * - bitmap.byteCount: Lấy tổng số bytes của bitmap
         * - Chia 1024: Chuyển từ bytes sang kilobytes
         * - LruCache sẽ dùng giá trị này để quản lý tổng dung lượng cache
         */
        override fun sizeOf(key: String, bitmap: Bitmap): Int {
            return bitmap.byteCount / 1024
        }
        
        /**
         * @param evicted: true nếu bị loại bỏ do thiếu bộ nhớ, false nếu bị xóa thủ công
         * @param key: Khóa của ảnh bị loại bỏ
         * @param oldValue: Bitmap bị loại bỏ
         * @param newValue: Bitmap thay thế (có thể null)
         * 
         * === KHI NÀO ĐƯỢC GỌI ===
         * - Cache đầy và cần xóa ảnh cũ (evicted = true)
         * - Gọi evictAll() để xóa toàn bộ cache (evicted = false)
         * - Put cùng key với giá trị mới (evicted = false)
         * 
         * === TẠI SAO CẦN RECYCLE ===
         * - Bitmap chiếm nhiều bộ nhớ native heap
         * - Không recycle → Memory leak → OutOfMemoryError
         * - recycle() giải phóng ngay lập tức, không chờ GC
         */
        override fun entryRemoved(evicted: Boolean, key: String, oldValue: Bitmap, newValue: Bitmap?) {
            if (evicted && !oldValue.isRecycled) {
                oldValue.recycle()
            }
        }
    }

    // cache cho ảnh gốc
    /**
     * === CHỨC NĂNG ===
     * - Lưu trữ ảnh gốc đã áp dụng filter với chất lượng cao (1024x1024 hoặc lớn hơn)
     * - Sử dụng một nửa kích thước cache (cacheSize/2) vì ảnh lớn hơn preview rất nhiều
     * - Giúp tránh việc phải tính toán lại filter khi user chọn lại cùng filter
     * 
     * === KHI NÀO SỬ DỤNG ===
     * - User áp dụng filter cho ảnh chính
     * - Chuyển đổi giữa các filter đã áp dụng trước đó
     * - Lưu ảnh cuối cùng với filter
     * 
     * === CHIẾN LƯỢC MEMORY ===
     * - Ưu tiên ảnh chính hơn preview (ảnh lớn quan trọng hơn)
     * - LRU đảm bảo giữ lại ảnh được sử dụng gần đây nhất
     */
    private val processedImageCache = object : LruCache<String, Bitmap>(cacheSize / 2) {
        override fun sizeOf(key: String, bitmap: Bitmap): Int {
            return bitmap.byteCount / 1024
        }

        override fun entryRemoved(evicted: Boolean, key: String, oldValue: Bitmap, newValue: Bitmap?) {
            if (evicted && !oldValue.isRecycled) {
                oldValue.recycle()
            }
        }
    }

    /**
     * @param originalBitmapHash: Hash của ảnh gốc để định danh duy nhất
     * @param filterType: Loại filter đã áp dụng
     * @param bitmap: Bitmap đã xử lý cần lưu vào cache
     *
     * === CÁCH HOẠT ĐỘNG ===
     * 1. Tạo key unique từ hash ảnh gốc + tên filter
     * 2. Lưu vào processedImageCache với key đó
     * 3. Nếu cache đầy: LRU tự động xóa ảnh ít dùng nhất
     * 4. Nếu cùng key: Ghi đè lên giá trị cũ
     *
     * === MEMORY MANAGEMENT ===
     * - Khi vượt quá cacheSize/2: Xóa ảnh cũ nhất
     * - Bitmap cũ được recycle tự động trong entryRemoved()
     */
    fun putProcessedImageInCache(originalBitmapHash: String, filterType: FilterType, bitmap: Bitmap) {
        val key = "${originalBitmapHash}_${filterType.name}_processed"
        processedImageCache.put(key, bitmap)
    }

    /**
     * @param originalBitmapHash: Hash của ảnh gốc để định danh duy nhất
     * @param filterType: Loại filter đã áp dụng (SEPIA, GRAYSCALE, etc.)
     * @return: Bitmap đã xử lý nếu có trong cache, null nếu không có
     * 
     * === CÁCH HOẠT ĐỘNG ===
     * 1. Tạo key unique từ hash ảnh gốc + tên filter
     * 2. Tìm kiếm trong processedImageCache
     * 3. Nếu tìm thấy: Trả về ngay (cache hit)
     * 4. Nếu không tìm thấy: Trả về null (cache miss) → Cần tính toán
     */
    fun getProcessedImageFromCache(originalBitmapHash: String, filterType: FilterType): Bitmap? {
        val key = "${originalBitmapHash}_${filterType.name}_processed"
        return processedImageCache.get(key)
    }

    // Xóa toàn bộ cache (CLEAR operation)
    /**
     * === KHI NÀO SỬ DỤNG ===
     * - Hệ thống thiếu bộ nhớ (onLowMemory callback)
     * - Ứng dụng chuyển sang background (onTrimMemory)
     * - User yêu cầu dọn dẹp bộ nhớ thủ công
     * - Memory pressure từ hệ thống Android
     * 
     * === CÁCH HOẠT ĐỘNG ===
     * - evictAll(): Xóa tất cả entries trong cache
     * - Tự động gọi entryRemoved() cho mỗi bitmap
     * - Tất cả bitmap được recycle() để giải phóng bộ nhớ
     * - Cache size về 0, sẵn sàng cho data mới
     */
    fun clearCache() {
        previewCache.evictAll()      // Xóa cache preview
        processedImageCache.evictAll() // Xóa cache ảnh đã xử lý
    }
}
