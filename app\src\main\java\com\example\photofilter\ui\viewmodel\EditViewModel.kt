package com.example.photofilter.ui.viewmodel

import android.content.ContentResolver
import android.content.Context
import android.graphics.Bitmap
import android.net.Uri
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.photofilter.domain.model.AdjustmentData
import com.example.photofilter.domain.model.AspectRatio
import com.example.photofilter.domain.model.CropData
import com.example.photofilter.domain.model.EditTab
import com.example.photofilter.domain.model.FilterType
import com.example.photofilter.domain.model.ImageData
import com.example.photofilter.domain.repository.ImageRepository
import com.example.photofilter.ui.action.EditScreenAction
import com.example.photofilter.ui.state.EditUiState
import com.example.photofilter.utils.ImageUtils
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.runBlocking

/**
 * EditViewModel (MVVM) - Simple MVVM architecture
 * 
 * Repository → ViewModel → UI pattern
 */
class EditViewModel(
    private val imageRepository: ImageRepository
) : ViewModel() {

    // ==================== STATE MANAGEMENT ====================
    
    private val _uiState = MutableStateFlow(EditUiState())
    val uiState: StateFlow<EditUiState> = _uiState.asStateFlow()
    
    // Debounce job cho adjustments để tránh lag
    private var adjustmentDebounceJob: Job? = null

    // ==================== ACTION HANDLER ====================
    
    /**
     * Handler thống nhất cho EditScreenAction (MVVM pattern)
     */
    fun handleAction(action: EditScreenAction) {
        when (action) {
            // ==================== UI ACTIONS ====================
            is EditScreenAction.SelectTab -> selectTab(action.tab)
            is EditScreenAction.ShowExitDialog -> showExitDialog()
            is EditScreenAction.HideExitDialog -> hideExitDialog()
            is EditScreenAction.RequestExit -> requestExit()
            is EditScreenAction.SaveAndExit -> saveAndExit()
            is EditScreenAction.ResetExitFlag -> resetExitFlag()

            // ==================== DOMAIN ACTIONS ====================
            is EditScreenAction.LoadImage -> loadImage(action.uri, action.context.contentResolver)
            is EditScreenAction.SaveImage -> saveImage(action.context)
            is EditScreenAction.ResetToOriginal -> resetToOriginal()
            is EditScreenAction.ClearImageData -> clearImageData()
            is EditScreenAction.RotateImage -> rotateImage(action.angle)
            is EditScreenAction.UpdateSliderRotation -> updateSliderRotation(action.value)
            is EditScreenAction.FlipHorizontal -> flipHorizontal()
            is EditScreenAction.ApplyFilter -> applyFilter(action.filterType)
            is EditScreenAction.UpdateAdjustments -> updateAdjustments(action.adjustments)
            is EditScreenAction.UpdateCropData -> updateCropData(action.cropData)
            is EditScreenAction.ApplyCropAspectRatio -> applyCropAspectRatio(action.aspectRatio)
            
            // ==================== UTILITY ACTIONS ====================
            is EditScreenAction.ClearError -> clearError()
            is EditScreenAction.ClearStatusMessage -> clearStatusMessage()
        }
    }

    // ==================== UI STATE FUNCTIONS ====================
    
    /**
     * Chọn tab trong EditScreen
     */
    private fun selectTab(tab: EditTab) {
        _uiState.value = _uiState.value.copy(selectedTab = tab)
    }

    /**
     * Hiển thị dialog xác nhận thoát
     */
    private fun showExitDialog() {
        _uiState.value = _uiState.value.copy(showExitDialog = true)
    }

    /**
     * Ẩn dialog xác nhận thoát
     */
    private fun hideExitDialog() {
        _uiState.value = _uiState.value.copy(
            showExitDialog = false,
            shouldExitAfterSave = false
        )
    }

    /**
     * Xử lý logic thoát screen với kiểm tra unsaved changes
     */
    private fun requestExit() {
        when {
            // Không có ảnh nào → Thoát ngay lập tức
            _uiState.value.imageData == null -> {
                _uiState.value = _uiState.value.copy(shouldExit = true)
            }
            // Có thay đổi chưa lưu → Hiện dialog xác nhận
            _uiState.value.hasUnsavedChanges -> {
                showExitDialog()
            }
            // Có ảnh nhưng không có thay đổi → Clear và thoát
            else -> {
                clearImageData()
                _uiState.value = _uiState.value.copy(shouldExit = true)
            }
        }
    }

    /**
     * Lưu ảnh và thoát
     */
    private fun saveAndExit() {
        _uiState.value = _uiState.value.copy(
            showExitDialog = false,
            shouldExitAfterSave = true
        )
    }

    /**
     * Reset exit flag sau khi đã xử lý
     */
    private fun resetExitFlag() {
        _uiState.value = _uiState.value.copy(shouldExit = false)
    }

    // ==================== DOMAIN LOGIC FUNCTIONS ====================
    
    /**
     * Tải ảnh từ URI với tối ưu memory (đã di chuyển logic sang repository)
     */
    fun loadImage(uri: Uri, contentResolver: ContentResolver) {
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isLoading = true, errorMessage = null)

            imageRepository.loadOptimizedImageFromUri(uri, contentResolver)
                .onSuccess { imageData ->
                    _uiState.value = _uiState.value.copy(
                        imageData = imageData,
                        isLoading = false,
                        statusMessage = "Đã tải ảnh thành công (${ImageUtils.getImageInfo(imageData.originalBitmap)})"
                    )
                }
                .onFailure { error ->
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        errorMessage = error.message ?: "Không thể tải ảnh",
                        statusMessage = "Lỗi khi tải ảnh"
                    )
                }
        }
    }

    /**
     * Áp dụng filter với cache support (đã tối ưu với repository)
     * Updated: Duy trì tất cả transforms và adjustments hiện có
     */
    fun applyFilter(filterType: FilterType) {
        val imageData = _uiState.value.imageData ?: return

        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isLoading = true)

            try {
                // Sử dụng repository để áp dụng filter với duy trì transforms và adjustments
                imageRepository.applyFilter(imageData, filterType)
                    .onSuccess { updatedImageData ->
                        _uiState.value = _uiState.value.copy(
                            imageData = updatedImageData,
                            isLoading = false,
                            statusMessage = when {
                                filterType == FilterType.ORIGINAL -> "Đã loại bỏ bộ lọc/hiệu ứng"
                                else -> "Đã áp dụng bộ lọc ${filterType.displayName}"
                            }
                        )
                        updateUnsavedChangesState()
                    }
                    .onFailure { error ->
                        _uiState.value = _uiState.value.copy(
                            isLoading = false,
                            errorMessage = "Lỗi khi áp dụng bộ lọc: ${error.message}"
                        )
                    }
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    errorMessage = "Lỗi khi áp dụng bộ lọc: ${e.message}"
                )
            }
        }
    }

    /**
     * Xoay ảnh với góc cụ thể
     */
    fun rotateImage(angle: Float = 90f) {
        val imageData = _uiState.value.imageData ?: return
        
        viewModelScope.launch {
            try {
                imageRepository.rotateImage(imageData, angle)
                    .onSuccess { updatedImageData ->
                        _uiState.value = _uiState.value.copy(
                            imageData = updatedImageData,
                            statusMessage = "Đã xoay ảnh ${angle.toInt()}°"
                        )
                        updateUnsavedChangesState()
                    }
                    .onFailure { error ->
                        _uiState.value = _uiState.value.copy(
                            errorMessage = error.message ?: "Không thể xoay ảnh"
                        )
                    }
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    errorMessage = "Lỗi khi xoay ảnh: ${e.message}"
                )
            }
        }
    }

    /**
     * Cập nhật góc xoay từ slider (fine-tune)
     */
    fun updateSliderRotation(value: Float) {
        val imageData = _uiState.value.imageData ?: return
        
        viewModelScope.launch {
            try {
                imageRepository.updateSliderRotation(imageData, value)
                    .onSuccess { updatedImageData ->
                        _uiState.value = _uiState.value.copy(
                            imageData = updatedImageData,
                            statusMessage = "Góc xoay: ${value.toInt()}°"
                        )
                        updateUnsavedChangesState()
                    }
                    .onFailure { error ->
                        _uiState.value = _uiState.value.copy(
                            errorMessage = error.message ?: "Không thể cập nhật góc xoay"
                        )
                    }
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    errorMessage = "Lỗi khi cập nhật góc xoay: ${e.message}"
                )
            }
        }
    }

    /**
     * Lật ảnh theo chiều ngang
     */
    fun flipHorizontal() {
        val imageData = _uiState.value.imageData ?: return
        
        viewModelScope.launch {
            try {
                imageRepository.flipHorizontal(imageData)
                    .onSuccess { updatedImageData ->
                        val newFlipHorizontal = updatedImageData.transformData.flipHorizontal
                        _uiState.value = _uiState.value.copy(
                            imageData = updatedImageData,
                            statusMessage = if (newFlipHorizontal == 1) "Đã lật ảnh theo chiều ngang" else "Đã bỏ lật ngang"
                        )
                        updateUnsavedChangesState()
                    }
                    .onFailure { error ->
                        _uiState.value = _uiState.value.copy(
                            errorMessage = error.message ?: "Không thể lật ảnh theo chiều ngang"
                        )
                    }
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    errorMessage = "Lỗi khi lật ảnh: ${e.message}"
                )
            }
        }
    }

    /**
     * Khôi phục ảnh về trạng thái gốc
     */
    fun resetToOriginal() {
        val imageData = _uiState.value.imageData ?: return
        
        viewModelScope.launch {
            try {
                imageRepository.resetToOriginal(imageData)
                    .onSuccess { updatedImageData ->
                        _uiState.value = _uiState.value.copy(
                            imageData = updatedImageData,
                            statusMessage = "Đã khôi phục ảnh gốc"
                        )
                    }
                    .onFailure { error ->
                        _uiState.value = _uiState.value.copy(
                            errorMessage = error.message ?: "Không thể khôi phục ảnh gốc"
                        )
                    }
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    errorMessage = "Lỗi khi khôi phục ảnh: ${e.message}"
                )
            }
        }
    }

    /**
     * Lưu ảnh vào Gallery
     */
    fun saveImage(context: Context) {
        val imageData = _uiState.value.imageData ?: return
        
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isSaving = true)
            
            try {
                imageRepository.saveImageToGallery(context, imageData.currentBitmap)
                    .onSuccess {
                        _uiState.value = _uiState.value.copy(
                            isSaving = false,
                            statusMessage = "Đã lưu ảnh thành công"
                        )
                    }
                    .onFailure { error ->
                        _uiState.value = _uiState.value.copy(
                            isSaving = false,
                            errorMessage = error.message ?: "Không thể lưu ảnh"
                        )
                    }
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    isSaving = false,
                    errorMessage = "Lỗi khi lưu ảnh: ${e.message}"
                )
            }
        }
    }

    /**
     * Điều chỉnh ảnh với debounce để tránh lag (sử dụng repository)
     */
    fun updateAdjustments(adjustments: AdjustmentData) {
        val currentImageData = _uiState.value.imageData ?: return

        // Cập nhật UI state ngay lập tức để responsive
        val updatedImageData = currentImageData.copy(adjustmentData = adjustments)
        _uiState.value = _uiState.value.copy(imageData = updatedImageData)
        
        // Cancel job trước đó và tạo job mới với debounce
        adjustmentDebounceJob?.cancel()
        
        adjustmentDebounceJob = viewModelScope.launch {
            delay(150) // Debounce 150ms
            
            _uiState.value = _uiState.value.copy(isLoading = true)

            try {
                imageRepository.applyAdjustments(
                    imageData = currentImageData,
                    brightness = adjustments.brightness,
                    contrast = adjustments.contrast,
                    saturation = adjustments.saturation,
                    blur = adjustments.blur,
                    sharpen = adjustments.sharpen
                ).onSuccess { finalImageData ->
                    _uiState.value = _uiState.value.copy(
                        imageData = finalImageData,
                        isLoading = false
                    )
                    updateUnsavedChangesState()
                }.onFailure { error ->
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        errorMessage = "Lỗi khi điều chỉnh ảnh: ${error.message}"
                    )
                }
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    errorMessage = "Lỗi khi điều chỉnh ảnh: ${e.message}"
                )
            }
        }
    }

    /**
     * Cập nhật crop data
     */
    fun updateCropData(cropData: CropData) {
        val currentImageData = _uiState.value.imageData ?: return

        val updatedImageData = currentImageData.copy(cropData = cropData)
        _uiState.value = _uiState.value.copy(imageData = updatedImageData)
        updateUnsavedChangesState()
    }

    /**
     * Áp dụng crop theo aspect ratio (sử dụng repository)
     */
    fun applyCropAspectRatio(aspectRatio: AspectRatio) {
        val currentImageData = _uiState.value.imageData ?: return

        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isLoading = true)

            try {
                imageRepository.applyCropAspectRatio(currentImageData, aspectRatio.ratio)
                    .onSuccess { updatedImageData ->
                        _uiState.value = _uiState.value.copy(
                            imageData = updatedImageData,
                            isLoading = false
                        )
                        updateUnsavedChangesState()
                    }
                    .onFailure { error ->
                        _uiState.value = _uiState.value.copy(
                            isLoading = false,
                            errorMessage = "Lỗi khi cắt ảnh: ${error.message}"
                        )
                    }
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    errorMessage = "Lỗi khi cắt ảnh: ${e.message}"
                )
            }
        }
    }

    // ==================== UTILITY FUNCTIONS ====================
    
    /**
     * Clear error message
     */
    fun clearError() {
        _uiState.value = _uiState.value.copy(errorMessage = null)
    }

    /**
     * Clear status message
     */
    fun clearStatusMessage() {
        _uiState.value = _uiState.value.copy(statusMessage = "")
    }

    /**
     * Lấy bitmap cho filter preview (sử dụng repository)
     * Updated: Bao gồm transforms và adjustments nhưng không có filter và crop
     */
    fun getTransformedBitmapWithoutFilter(): Bitmap? {
        val imageData = _uiState.value.imageData ?: return null
        
        return try {
            // Tạo imageData tạm thời không có filter và crop để preview
            val tempImageData = imageData.copy(
                filterData = imageData.filterData.copy(appliedFilter = FilterType.ORIGINAL),
                cropData = com.example.photofilter.domain.model.CropData() // Reset crop để hiển thị ảnh đầy đủ cho preview
            )
            
            // Sử dụng repository để lấy bitmap với transforms và adjustments
            runBlocking {
                imageRepository.getBaseTransformedBitmap(tempImageData)
                    .getOrNull()
            }
        } catch (e: Exception) {
            imageData.originalBitmap
        }
    }

    /**
     * Áp dụng filter cho preview với performance optimization
     */
    suspend fun applyFilterToPreview(bitmap: Bitmap, filterType: FilterType): Bitmap {
        return try {
            if (filterType == FilterType.ORIGINAL) {
                bitmap
            } else {
                val imageData = ImageData(
                    originalBitmap = bitmap,
                    currentBitmap = bitmap
                )
                imageRepository.applyFilter(imageData, filterType)
                    .getOrNull()?.currentBitmap ?: bitmap
            }
        } catch (e: Exception) {
            bitmap
        }
    }

    /**
     * Clear toàn bộ image data và reset UI state
     */
    fun clearImageData() {
        _uiState.value = EditUiState()
    }

    /**
     * Cập nhật trạng thái unsaved changes
     */
    private fun updateUnsavedChangesState() {
        val currentImageData = _uiState.value.imageData
        val hasChanges = currentImageData?.hasUnsavedChanges() ?: false
        _uiState.value = _uiState.value.copy(hasUnsavedChanges = hasChanges)
    }
    
    /**
     * Cleanup khi ViewModel bị destroy
     */
    override fun onCleared() {
        super.onCleared()
        adjustmentDebounceJob?.cancel()
    }
}
