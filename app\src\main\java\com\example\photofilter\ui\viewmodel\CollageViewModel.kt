package com.example.photofilter.ui.viewmodel

import android.content.ContentResolver
import android.content.Context
import android.graphics.Bitmap
import android.net.Uri
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.photofilter.domain.model.CollageData
import com.example.photofilter.domain.model.CollageLayout
import com.example.photofilter.domain.repository.CollageRepository
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch

data class CollageUiState(
    val images: List<Bitmap> = emptyList(),
    val selectedLayout: CollageLayout = CollageLayout.GRID_2X2,
    val collageData: CollageData? = null,
    val isLoading: Boolean = false,
    val isSaving: Boolean = false,
    val errorMessage: String? = null,
    val statusMessage: String = "Chọn ảnh để tạo collage",
    val showExitDialog: Boolean = false,
    val shouldExit: Boolean = false,
    val shouldExitAfterSave: Boolean = false
)

class CollageViewModel(
    private val collageRepository: CollageRepository
) : ViewModel() {

    private val _uiState = MutableStateFlow(CollageUiState())
    val uiState: StateFlow<CollageUiState> = _uiState.asStateFlow()

    fun loadImages(uris: List<Uri>, contentResolver: ContentResolver) {
        if (uris.isEmpty()) return
        
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isLoading = true, errorMessage = null)
            
            collageRepository.loadImages(uris, contentResolver)
                .onSuccess { bitmaps ->
                    _uiState.value = _uiState.value.copy(
                        images = bitmaps,
                        isLoading = false,
                        statusMessage = "Đã tải ${bitmaps.size} ảnh"
                    )
                    // Auto create collage with current layout
                    createCollage()
                }
                .onFailure { error ->
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        errorMessage = error.message ?: "Không thể tải ảnh"
                    )
                }
        }
    }

    fun selectLayout(layout: CollageLayout) {
        _uiState.value = _uiState.value.copy(selectedLayout = layout)
        if (_uiState.value.images.isNotEmpty()) {
            createCollage()
        }
    }

    private fun createCollage() {
        val currentImages = _uiState.value.images
        val currentLayout = _uiState.value.selectedLayout
        
        if (currentImages.isEmpty()) return
        
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isLoading = true)
            
            // Take only the required number of images for the layout
            val imagesToUse = currentImages.take(currentLayout.maxImages)
            
            collageRepository.createCollage(imagesToUse, currentLayout)
                .onSuccess { collageData ->
                    _uiState.value = _uiState.value.copy(
                        collageData = collageData,
                        isLoading = false,
                        statusMessage = "Đã tạo collage ${currentLayout.displayName}"
                    )
                }
                .onFailure { error ->
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        errorMessage = error.message ?: "Không thể tạo collage"
                    )
                }
        }
    }

    fun saveCollage(context: Context, fileName: String = "") {
        val currentCollageData = _uiState.value.collageData ?: return
        
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isSaving = true)
            
            collageRepository.saveCollage(context, currentCollageData, fileName)
                .onSuccess {
                    _uiState.value = _uiState.value.copy(
                        isSaving = false,
                        statusMessage = "Đã lưu collage thành công"
                    )
                }
                .onFailure { error ->
                    _uiState.value = _uiState.value.copy(
                        isSaving = false,
                        errorMessage = error.message ?: "Không thể lưu collage"
                    )
                }
        }
    }

    fun clearImages() {
        _uiState.value = _uiState.value.copy(
            images = emptyList(),
            collageData = null,
            statusMessage = "Chọn ảnh để tạo collage"
        )
    }

    fun clearError() {
        _uiState.value = _uiState.value.copy(errorMessage = null)
    }

    // Exit handling methods
    fun requestExit() {
        val hasUnsavedCollage = _uiState.value.collageData != null
        if (hasUnsavedCollage) {
            _uiState.value = _uiState.value.copy(showExitDialog = true)
        } else {
            _uiState.value = _uiState.value.copy(shouldExit = true)
        }
    }

    fun hideExitDialog() {
        _uiState.value = _uiState.value.copy(showExitDialog = false)
    }

    fun clearCollageData() {
        _uiState.value = _uiState.value.copy(
            images = emptyList(),
            collageData = null,
            statusMessage = "Chọn ảnh để tạo collage",
            shouldExit = true
        )
    }

    fun saveAndExit() {
        _uiState.value = _uiState.value.copy(
            showExitDialog = false,
            shouldExitAfterSave = true
        )
    }

    fun resetExitFlag() {
        _uiState.value = _uiState.value.copy(shouldExit = false)
    }

    fun clearStatusMessage() {
        _uiState.value = _uiState.value.copy(statusMessage = "")
    }
}
