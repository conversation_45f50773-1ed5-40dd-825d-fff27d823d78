package com.example.photofilter.utils

import android.graphics.Bitmap
import android.graphics.Canvas
import android.graphics.Paint
import android.graphics.Rect
import androidx.core.graphics.createBitmap
import androidx.core.graphics.scale

/**
 * Bộ tiện ích tạo Collage - Ghép nhiều ảnh thành một ảnh duy nhất
 * 
 * === MỤC ĐÍCH CHÍNH ===
 * - Tạo collage theo các layout khác nhau: Grid, Horizontal, Vertical
 * - Tự động scale ảnh để fit vào từng cell
 * - Vẽ ảnh vào đúng vị trí với chất lượng cao
 * - Hỗ trợ nhiều kích thước và tỷ lệ collage
 *
 * === TECHNICAL APPROACH ===
 * - Canvas-based drawing: Vẽ từng ảnh lên canvas chung
 * - Smart scaling: Tự động scale ảnh giữ tỷ lệ
 * - Anti-alias paint: Chất lượng cao, không răng cưa
 * - Memory efficient: T<PERSON>i sử dụng Paint object
 * 
 * === OUTPUT QUALITY ===
 * - High resolution: 1024x1024+ cho grid layouts
 * - Consistent sizing: Mỗi cell có kích thước chuẩn
 * - Smooth scaling: Không bị pixelated
 * - Professional looking: Suitable cho social media
 */
object CollageUtils {

    // ==================== GRID LAYOUTS - LƯỚI VUÔNG ====================
    /**
     * @param images: Danh sách bitmap (tối thiểu 1, tối đa 4 ảnh được sử dụng)
     * 
     * === SPECIFICATIONS ===
     * - Cell size: 512x512px mỗi ô
     * - Total size: 1024x1024px
     */
    fun createGrid2x2Collage(images: List<Bitmap>): Bitmap {
        val cellSize = 512
        val collageSize = cellSize * 2 // 1024x1024
        val collageBitmap = createBitmap(collageSize, collageSize)
        val canvas = Canvas(collageBitmap)
        val paint = Paint(Paint.ANTI_ALIAS_FLAG) // Smooth, không răng cưa

        // Định nghĩa 4 vị trí cho grid 2x2
        val positions = listOf(
            Rect(0, 0, cellSize, cellSize),           // Top-left
            Rect(cellSize, 0, collageSize, cellSize), // Top-right
            Rect(0, cellSize, cellSize, collageSize), // Bottom-left
            Rect(cellSize, cellSize, collageSize, collageSize) // Bottom-right
        )

        drawImagesInPositions(canvas, images, positions, cellSize, cellSize, paint)
        return collageBitmap
    }

    /**
     * Tạo collage theo layout Grid 3x3 (9 ảnh trong lưới vuông)
     * 
     * @param images: Danh sách bitmap (tối thiểu 1, tối đa 9 ảnh được sử dụng)
     * @return: Bitmap collage 1023x1023px
     * 
     * === SPECIFICATIONS ===
     * - Cell size: 341x341px mỗi ô
     * - Total size: 1023x1023px (341*3)
     */
    fun createGrid3x3Collage(images: List<Bitmap>): Bitmap {
        val cellSize = 341
        val collageSize = cellSize * 3 // 1023x1023
        val collageBitmap = createBitmap(collageSize, collageSize)
        val canvas = Canvas(collageBitmap)
        val paint = Paint(Paint.ANTI_ALIAS_FLAG)

        val positions = mutableListOf<Rect>()
        for (row in 0 until 3) {
            for (col in 0 until 3) {
                positions.add(
                    Rect(
                        col * cellSize,
                        row * cellSize,
                        (col + 1) * cellSize,
                        (row + 1) * cellSize
                    )
                )
            }
        }

        drawImagesInPositions(canvas, images, positions, cellSize, cellSize, paint)
        return collageBitmap
    }

    /**
     * Tạo collage theo layout Horizontal
     */
    fun createHorizontalCollage(images: List<Bitmap>, count: Int): Bitmap {
        val cellWidth = 512
        val cellHeight = 768
        val collageWidth = cellWidth * count
        val collageBitmap = createBitmap(collageWidth, cellHeight)
        val canvas = Canvas(collageBitmap)
        val paint = Paint(Paint.ANTI_ALIAS_FLAG)

        images.forEachIndexed { index, bitmap ->
            if (index < count) {
                val scaledBitmap = bitmap.scale(cellWidth, cellHeight)
                val left = index * cellWidth
                canvas.drawBitmap(scaledBitmap, left.toFloat(), 0f, paint)
            }
        }

        return collageBitmap
    }

    /**
     * Tạo collage theo layout Vertical
     */
    fun createVerticalCollage(images: List<Bitmap>, count: Int): Bitmap {
        val cellWidth = 768
        val cellHeight = 512
        val collageHeight = cellHeight * count
        val collageBitmap = createBitmap(cellWidth, collageHeight)
        val canvas = Canvas(collageBitmap)
        val paint = Paint(Paint.ANTI_ALIAS_FLAG)

        images.forEachIndexed { index, bitmap ->
            if (index < count) {
                val scaledBitmap = bitmap.scale(cellWidth, cellHeight)
                val top = index * cellHeight
                canvas.drawBitmap(scaledBitmap, 0f, top.toFloat(), paint)
            }
        }

        return collageBitmap
    }

    /**
     * Helper function để vẽ images vào các positions định sẵn
     * Dùng chung cho Grid layouts
     */
    private fun drawImagesInPositions(
        canvas: Canvas,
        images: List<Bitmap>,
        positions: List<Rect>,
        cellWidth: Int,
        cellHeight: Int,
        paint: Paint
    ) {
        images.forEachIndexed { index, bitmap ->
            if (index < positions.size) {
                val scaledBitmap = bitmap.scale(cellWidth, cellHeight)
                canvas.drawBitmap(scaledBitmap, null, positions[index], paint)
            }
        }
    }
}
